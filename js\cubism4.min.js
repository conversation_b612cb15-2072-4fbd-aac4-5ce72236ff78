!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@pixi/core"),require("@pixi/display")):"function"==typeof define&&define.amd?define(["exports","@pixi/core","@pixi/display"],e):e(((t="undefined"!=typeof globalThis?globalThis:t||self).PIXI=t.PIXI||{},t.PIXI.live2d=t.PIXI.live2d||{}),t.PIXI,t.PIXI)}(this,(function(t,e,i){"use strict";var s=Object.defineProperty,r=Math.pow,o=(t,e,i)=>(((t,e,i)=>{e in t?s(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i})(t,"symbol"!=typeof e?e+"":e,i),i),a=(t,e,i)=>new Promise(((s,r)=>{var o=t=>{try{n(i.next(t))}catch(e){r(e)}},a=t=>{try{n(i.throw(t))}catch(e){r(e)}},n=t=>t.done?s(t.value):Promise.resolve(t.value).then(o,a);n((i=i.apply(t,e)).next())}));class n{constructor(){this._breathParameters=[],this._currentTime=0}static create(){return new n}setParameters(t){this._breathParameters=t}getParameters(){return this._breathParameters}updateParameters(t,e){this._currentTime+=e;const i=2*this._currentTime*3.14159;for(let s=0;s<this._breathParameters.length;++s){const e=this._breathParameters[s];t.addParameterValueById(e.parameterId,e.offset+e.peak*Math.sin(i/e.cycle),e.weight)}}}class l{constructor(t,e,i,s,r){this.parameterId=null==t?void 0:t,this.offset=null==e?0:e,this.peak=null==i?0:i,this.cycle=null==s?0:s,this.weight=null==r?0:r}}const h=class t{static create(e){return new t(e)}setBlinkingInterval(t){this._blinkingIntervalSeconds=t}setBlinkingSetting(t,e,i){this._closingSeconds=t,this._closedSeconds=e,this._openingSeconds=i}setParameterIds(t){this._parameterIds=t}getParameterIds(){return this._parameterIds}updateParameters(e,i){let s;this._userTimeSeconds+=i;let r=0;switch(this._blinkingState){case 2:r=(this._userTimeSeconds-this._stateStartTimeSeconds)/this._closingSeconds,r>=1&&(r=1,this._blinkingState=3,this._stateStartTimeSeconds=this._userTimeSeconds),s=1-r;break;case 3:r=(this._userTimeSeconds-this._stateStartTimeSeconds)/this._closedSeconds,r>=1&&(this._blinkingState=4,this._stateStartTimeSeconds=this._userTimeSeconds),s=0;break;case 4:r=(this._userTimeSeconds-this._stateStartTimeSeconds)/this._openingSeconds,r>=1&&(r=1,this._blinkingState=1,this._nextBlinkingTime=this.determinNextBlinkingTiming()),s=r;break;case 1:this._nextBlinkingTime<this._userTimeSeconds&&(this._blinkingState=2,this._stateStartTimeSeconds=this._userTimeSeconds),s=1;break;default:this._blinkingState=1,this._nextBlinkingTime=this.determinNextBlinkingTiming(),s=1}t.CloseIfZero||(s=-s);for(let t=0;t<this._parameterIds.length;++t)e.setParameterValueById(this._parameterIds[t],s)}constructor(t){var e,i;this._blinkingState=0,this._nextBlinkingTime=0,this._stateStartTimeSeconds=0,this._blinkingIntervalSeconds=4,this._closingSeconds=.1,this._closedSeconds=.05,this._openingSeconds=.15,this._userTimeSeconds=0,this._parameterIds=[],null!=t&&(this._parameterIds=null!=(i=null==(e=t.getEyeBlinkParameters())?void 0:e.slice())?i:this._parameterIds)}determinNextBlinkingTiming(){const t=Math.random();return this._userTimeSeconds+t*(2*this._blinkingIntervalSeconds-1)}};h.CloseIfZero=!0;let u=h;var d=(t=>(t[t.EyeState_First=0]="EyeState_First",t[t.EyeState_Interval=1]="EyeState_Interval",t[t.EyeState_Closing=2]="EyeState_Closing",t[t.EyeState_Closed=3]="EyeState_Closed",t[t.EyeState_Opening=4]="EyeState_Opening",t))(d||{});class c{static create(t){const e=new c;"number"==typeof t.FadeInTime&&(e._fadeTimeSeconds=t.FadeInTime,e._fadeTimeSeconds<=0&&(e._fadeTimeSeconds=.5));const i=t.Groups,s=i.length;for(let r=0;r<s;++r){const t=i[r],s=t.length;let o=0;for(let i=0;i<s;++i){const s=t[i],r=new g;r.partId=s.Id;const a=s.Link;if(a){const t=a.length;for(let e=0;e<t;++e){const t=new g;t.partId=a[e],r.link.push(t)}}e._partGroups.push(r),++o}e._partGroupCounts.push(o)}return e}updateParameters(t,e){t!=this._lastModel&&this.reset(t),this._lastModel=t,e<0&&(e=0);let i=0;for(let s=0;s<this._partGroupCounts.length;s++){const r=this._partGroupCounts[s];this.doFade(t,e,i,r),i+=r}this.copyPartOpacities(t)}reset(t){let e=0;for(let i=0;i<this._partGroupCounts.length;++i){const s=this._partGroupCounts[i];for(let i=e;i<e+s;++i){this._partGroups[i].initialize(t);const s=this._partGroups[i].partIndex,r=this._partGroups[i].parameterIndex;if(!(s<0)){t.setPartOpacityByIndex(s,i==e?1:0),t.setParameterValueByIndex(r,i==e?1:0);for(let e=0;e<this._partGroups[i].link.length;++e)this._partGroups[i].link[e].initialize(t)}}e+=s}}copyPartOpacities(t){for(let e=0;e<this._partGroups.length;++e){const i=this._partGroups[e];if(0==i.link.length)continue;const s=this._partGroups[e].partIndex,r=t.getPartOpacityByIndex(s);for(let e=0;e<i.link.length;++e){const s=i.link[e].partIndex;s<0||t.setPartOpacityByIndex(s,r)}}}doFade(t,e,i,s){let r=-1,o=1;const a=.5;for(let n=i;n<i+s;++n){const i=this._partGroups[n].partIndex,s=this._partGroups[n].parameterIndex;if(t.getParameterValueByIndex(s)>.001){if(r>=0)break;r=n,o=t.getPartOpacityByIndex(i),o+=e/this._fadeTimeSeconds,o>1&&(o=1)}}r<0&&(r=0,o=1);for(let n=i;n<i+s;++n){const e=this._partGroups[n].partIndex;if(r==n)t.setPartOpacityByIndex(e,o);else{let i,s=t.getPartOpacityByIndex(e);i=o<a?-.5*o/a+1:(1-o)*a/.5;(1-i)*(1-o)>.15&&(i=1-.15/(1-o)),s>i&&(s=i),t.setPartOpacityByIndex(e,s)}}}constructor(){this._fadeTimeSeconds=.5,this._lastModel=void 0,this._partGroups=[],this._partGroupCounts=[]}}class g{constructor(t){this.parameterIndex=0,this.partIndex=0,this.partId="",this.link=[],null!=t&&this.assignment(t)}assignment(t){return this.partId=t.partId,this.link=t.link.map((t=>t.clone())),this}initialize(t){this.parameterIndex=t.getParameterIndex(this.partId),this.partIndex=t.getPartIndex(this.partId),t.setParameterValueByIndex(this.parameterIndex,1)}clone(){const t=new g;return t.partId=this.partId,t.parameterIndex=this.parameterIndex,t.partIndex=this.partIndex,t.link=this.link.map((t=>t.clone())),t}}class m{constructor(t,e){this.x=t||0,this.y=e||0}add(t){const e=new m(0,0);return e.x=this.x+t.x,e.y=this.y+t.y,e}substract(t){const e=new m(0,0);return e.x=this.x-t.x,e.y=this.y-t.y,e}multiply(t){const e=new m(0,0);return e.x=this.x*t.x,e.y=this.y*t.y,e}multiplyByScaler(t){return this.multiply(new m(t,t))}division(t){const e=new m(0,0);return e.x=this.x/t.x,e.y=this.y/t.y,e}divisionByScalar(t){return this.division(new m(t,t))}getLength(){return Math.sqrt(this.x*this.x+this.y*this.y)}getDistanceWith(t){return Math.sqrt((this.x-t.x)*(this.x-t.x)+(this.y-t.y)*(this.y-t.y))}dot(t){return this.x*t.x+this.y*t.y}normalize(){const t=Math.pow(this.x*this.x+this.y*this.y,.5);this.x=this.x/t,this.y=this.y/t}isEqual(t){return this.x==t.x&&this.y==t.y}isNotEqual(t){return!this.isEqual(t)}}const p=class t{static range(t,e,i){return t<e?t=e:t>i&&(t=i),t}static sin(t){return Math.sin(t)}static cos(t){return Math.cos(t)}static abs(t){return Math.abs(t)}static sqrt(t){return Math.sqrt(t)}static cbrt(t){if(0===t)return t;let e=t;const i=e<0;let s;return i&&(e=-e),e===1/0?s=1/0:(s=Math.exp(Math.log(e)/3),s=(e/(s*s)+2*s)/3),i?-s:s}static getEasingSine(t){return t<0?0:t>1?1:.5-.5*this.cos(t*Math.PI)}static max(t,e){return t>e?t:e}static min(t,e){return t>e?e:t}static degreesToRadian(t){return t/180*Math.PI}static radianToDegrees(t){return 180*t/Math.PI}static directionToRadian(t,e){let i=Math.atan2(e.y,e.x)-Math.atan2(t.y,t.x);for(;i<-Math.PI;)i+=2*Math.PI;for(;i>Math.PI;)i-=2*Math.PI;return i}static directionToDegrees(t,e){const i=this.directionToRadian(t,e);let s=this.radianToDegrees(i);return e.x-t.x>0&&(s=-s),s}static radianToDirection(t){const e=new m;return e.x=this.sin(t),e.y=this.cos(t),e}static quadraticEquation(e,i,s){return this.abs(e)<t.Epsilon?this.abs(i)<t.Epsilon?-s:-s/i:-(i+this.sqrt(i*i-4*e*s))/(2*e)}static cardanoAlgorithmForBezier(e,i,s,r){if(this.sqrt(e)<t.Epsilon)return this.range(this.quadraticEquation(i,s,r),0,1);const o=i/e,a=s/e,n=(3*a-o*o)/3,l=n/3,h=(2*o*o*o-9*o*a+27*(r/e))/27,u=h/2,d=u*u+l*l*l,c=.5,g=.51;if(d<0){const t=-n/3,e=t*t*t,i=this.sqrt(e),s=-h/(2*i),r=this.range(s,-1,1),a=Math.acos(r),l=2*this.cbrt(i),u=l*this.cos(a/3)-o/3;if(this.abs(u-c)<g)return this.range(u,0,1);const d=l*this.cos((a+2*Math.PI)/3)-o/3;if(this.abs(d-c)<g)return this.range(d,0,1);const m=l*this.cos((a+4*Math.PI)/3)-o/3;return this.range(m,0,1)}if(0==d){let t;t=u<0?this.cbrt(-u):-this.cbrt(u);const e=2*t-o/3;if(this.abs(e-c)<g)return this.range(e,0,1);const i=-t-o/3;return this.range(i,0,1)}const m=this.sqrt(d),p=this.cbrt(m-u)-this.cbrt(m+u)-o/3;return this.range(p,0,1)}constructor(){}};p.Epsilon=1e-5;let _=p;class f{constructor(){this._tr=new Float32Array(16),this.loadIdentity()}static multiply(t,e,i){const s=new Float32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]);for(let r=0;r<4;++r)for(let i=0;i<4;++i)for(let o=0;o<4;++o)s[i+4*r]+=t[o+4*r]*e[i+4*o];for(let r=0;r<16;++r)i[r]=s[r]}loadIdentity(){const t=new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]);this.setMatrix(t)}setMatrix(t){for(let e=0;e<16;++e)this._tr[e]=t[e]}getArray(){return this._tr}getScaleX(){return this._tr[0]}getScaleY(){return this._tr[5]}getTranslateX(){return this._tr[12]}getTranslateY(){return this._tr[13]}transformX(t){return this._tr[0]*t+this._tr[12]}transformY(t){return this._tr[5]*t+this._tr[13]}invertTransformX(t){return(t-this._tr[12])/this._tr[0]}invertTransformY(t){return(t-this._tr[13])/this._tr[5]}translateRelative(t,e){const i=new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,t,e,0,1]);f.multiply(i,this._tr,this._tr)}translate(t,e){this._tr[12]=t,this._tr[13]=e}translateX(t){this._tr[12]=t}translateY(t){this._tr[13]=t}scaleRelative(t,e){const i=new Float32Array([t,0,0,0,0,e,0,0,0,0,1,0,0,0,0,1]);f.multiply(i,this._tr,this._tr)}scale(t,e){this._tr[0]=t,this._tr[5]=e}multiplyByMatrix(t){f.multiply(t.getArray(),this._tr,this._tr)}clone(){const t=new f;for(let e=0;e<this._tr.length;e++)t._tr[e]=this._tr[e];return t}}class x{initialize(t){this._model=t}drawModel(){null!=this.getModel()&&(this.saveProfile(),this.doDrawModel(),this.restoreProfile())}setMvpMatrix(t){this._mvpMatrix4x4.setMatrix(t.getArray())}getMvpMatrix(){return this._mvpMatrix4x4}setModelColor(t,e,i,s){t<0?t=0:t>1&&(t=1),e<0?e=0:e>1&&(e=1),i<0?i=0:i>1&&(i=1),s<0?s=0:s>1&&(s=1),this._modelColor.R=t,this._modelColor.G=e,this._modelColor.B=i,this._modelColor.A=s}getModelColor(){return Object.assign({},this._modelColor)}setIsPremultipliedAlpha(t){this._isPremultipliedAlpha=t}isPremultipliedAlpha(){return this._isPremultipliedAlpha}setIsCulling(t){this._isCulling=t}isCulling(){return this._isCulling}setAnisotropy(t){this._anisotropy=t}getAnisotropy(){return this._anisotropy}getModel(){return this._model}useHighPrecisionMask(t){this._useHighPrecisionMask=t}isUsingHighPrecisionMask(){return this._useHighPrecisionMask}constructor(){this._isCulling=!1,this._isPremultipliedAlpha=!1,this._anisotropy=0,this._modelColor=new y,this._useHighPrecisionMask=!1,this._mvpMatrix4x4=new f,this._mvpMatrix4x4.loadIdentity()}}var C=(t=>(t[t.CubismBlendMode_Normal=0]="CubismBlendMode_Normal",t[t.CubismBlendMode_Additive=1]="CubismBlendMode_Additive",t[t.CubismBlendMode_Multiplicative=2]="CubismBlendMode_Multiplicative",t))(C||{});class y{constructor(t=1,e=1,i=1,s=1){this.R=t,this.G=e,this.B=i,this.A=s}}let M,v=!1,P=!1;const b={vertexOffset:0,vertexStep:2};class S{static startUp(t){if(v)return E("CubismFramework.startUp() is already done."),v;if(Live2DCubismCore._isStarted)return v=!0,!0;if(Live2DCubismCore._isStarted=!0,M=t,M&&Live2DCubismCore.Logging.csmSetLogFunction(M.logFunction),v=!0,v){const t=Live2DCubismCore.Version.csmGetVersion(),e=(16711680&t)>>16,i=65535&t,s=t;E("Live2D Cubism Core version: {0}.{1}.{2} ({3})",("00"+((4278190080&t)>>24)).slice(-2),("00"+e).slice(-2),("0000"+i).slice(-4),s)}return E("CubismFramework.startUp() is complete."),v}static cleanUp(){v=!1,P=!1,M=void 0}static initialize(t=0){v?P?L("CubismFramework.initialize() skipped, already initialized."):(Live2DCubismCore.Memory.initializeAmountOfMemory(t),P=!0,E("CubismFramework.initialize() is complete.")):L("CubismFramework is not started.")}static dispose(){v?P?(x.staticRelease(),P=!1,E("CubismFramework.dispose() is complete.")):L("CubismFramework.dispose() skipped, not initialized."):L("CubismFramework is not started.")}static isStarted(){return v}static isInitialized(){return P}static coreLogFunction(t){Live2DCubismCore.Logging.csmGetLogFunction()&&Live2DCubismCore.Logging.csmGetLogFunction()(t)}static getLoggingLevel(){return null!=M?M.loggingLevel:5}constructor(){}}var w=(t=>(t[t.LogLevel_Verbose=0]="LogLevel_Verbose",t[t.LogLevel_Debug=1]="LogLevel_Debug",t[t.LogLevel_Info=2]="LogLevel_Info",t[t.LogLevel_Warning=3]="LogLevel_Warning",t[t.LogLevel_Error=4]="LogLevel_Error",t[t.LogLevel_Off=5]="LogLevel_Off",t))(w||{});const T="undefined"!=typeof process&&"production"===process.env.NODE_ENV?()=>{}:t=>console.assert(t);function I(t,...e){A.print(w.LogLevel_Debug,"[CSM][D]"+t+"\n",e)}function E(t,...e){A.print(w.LogLevel_Info,"[CSM][I]"+t+"\n",e)}function L(t,...e){A.print(w.LogLevel_Warning,"[CSM][W]"+t+"\n",e)}function F(t,...e){A.print(w.LogLevel_Error,"[CSM][E]"+t+"\n",e)}class A{static print(t,e,i){if(t<S.getLoggingLevel())return;const s=S.coreLogFunction;if(!s)return;s(e.replace(/{(\d+)}/g,((t,e)=>i[e])))}static dumpBytes(t,e,i){for(let s=0;s<i;s++)s%16==0&&s>0?this.print(t,"\n"):s%8==0&&s>0&&this.print(t,"  "),this.print(t,"{0} ",[255&e[s]]);this.print(t,"\n")}constructor(){}}class D{constructor(t=!1,e=new y){this.isOverwritten=t,this.Color=e}}class B{constructor(t=!1,e=new y){this.isOverwritten=t,this.Color=e}}class R{constructor(t=!1,e=!1){this.isOverwritten=t,this.isCulling=e}}class O{update(){this._model.update(),this._model.drawables.resetDynamicFlags()}getPixelsPerUnit(){return null==this._model?0:this._model.canvasinfo.PixelsPerUnit}getCanvasWidth(){return null==this._model?0:this._model.canvasinfo.CanvasWidth/this._model.canvasinfo.PixelsPerUnit}getCanvasHeight(){return null==this._model?0:this._model.canvasinfo.CanvasHeight/this._model.canvasinfo.PixelsPerUnit}saveParameters(){const t=this._model.parameters.count,e=this._savedParameters.length;for(let i=0;i<t;++i)i<e?this._savedParameters[i]=this._parameterValues[i]:this._savedParameters.push(this._parameterValues[i])}getMultiplyColor(t){if(this.getOverwriteFlagForModelMultiplyColors()||this.getOverwriteFlagForDrawableMultiplyColors(t))return this._userMultiplyColors[t].Color;return this.getDrawableMultiplyColor(t)}getScreenColor(t){if(this.getOverwriteFlagForModelScreenColors()||this.getOverwriteFlagForDrawableScreenColors(t))return this._userScreenColors[t].Color;return this.getDrawableScreenColor(t)}setMultiplyColorByTextureColor(t,e){this.setMultiplyColorByRGBA(t,e.R,e.G,e.B,e.A)}setMultiplyColorByRGBA(t,e,i,s,r=1){this._userMultiplyColors[t].Color.R=e,this._userMultiplyColors[t].Color.G=i,this._userMultiplyColors[t].Color.B=s,this._userMultiplyColors[t].Color.A=r}setScreenColorByTextureColor(t,e){this.setScreenColorByRGBA(t,e.R,e.G,e.B,e.A)}setScreenColorByRGBA(t,e,i,s,r=1){this._userScreenColors[t].Color.R=e,this._userScreenColors[t].Color.G=i,this._userScreenColors[t].Color.B=s,this._userScreenColors[t].Color.A=r}getPartMultiplyColor(t){return this._userPartMultiplyColors[t].Color}getPartScreenColor(t){return this._userPartScreenColors[t].Color}setPartColor(t,e,i,s,r,o,a){if(o[t].Color.R=e,o[t].Color.G=i,o[t].Color.B=s,o[t].Color.A=r,o[t].isOverwritten)for(let n=0;n<this._partChildDrawables[t].length;++n){const o=this._partChildDrawables[t][n];a[o].Color.R=e,a[o].Color.G=i,a[o].Color.B=s,a[o].Color.A=r}}setPartMultiplyColorByTextureColor(t,e){this.setPartMultiplyColorByRGBA(t,e.R,e.G,e.B,e.A)}setPartMultiplyColorByRGBA(t,e,i,s,r){this.setPartColor(t,e,i,s,r,this._userPartMultiplyColors,this._userMultiplyColors)}setPartScreenColorByTextureColor(t,e){this.setPartScreenColorByRGBA(t,e.R,e.G,e.B,e.A)}setPartScreenColorByRGBA(t,e,i,s,r){this.setPartColor(t,e,i,s,r,this._userPartScreenColors,this._userScreenColors)}getOverwriteFlagForModelMultiplyColors(){return this._isOverwrittenModelMultiplyColors}getOverwriteFlagForModelScreenColors(){return this._isOverwrittenModelScreenColors}setOverwriteFlagForModelMultiplyColors(t){this._isOverwrittenModelMultiplyColors=t}setOverwriteFlagForModelScreenColors(t){this._isOverwrittenModelScreenColors=t}getOverwriteFlagForDrawableMultiplyColors(t){return this._userMultiplyColors[t].isOverwritten}getOverwriteFlagForDrawableScreenColors(t){return this._userScreenColors[t].isOverwritten}setOverwriteFlagForDrawableMultiplyColors(t,e){this._userMultiplyColors[t].isOverwritten=e}setOverwriteFlagForDrawableScreenColors(t,e){this._userScreenColors[t].isOverwritten=e}getOverwriteColorForPartMultiplyColors(t){return this._userPartMultiplyColors[t].isOverwritten}getOverwriteColorForPartScreenColors(t){return this._userPartScreenColors[t].isOverwritten}setOverwriteColorForPartColors(t,e,i,s){i[t].isOverwritten=e;for(let r=0;r<this._partChildDrawables[t].length;++r){const o=this._partChildDrawables[t][r];s[o].isOverwritten=e,e&&(s[o].Color.R=i[t].Color.R,s[o].Color.G=i[t].Color.G,s[o].Color.B=i[t].Color.B,s[o].Color.A=i[t].Color.A)}}setOverwriteColorForPartMultiplyColors(t,e){this._userPartMultiplyColors[t].isOverwritten=e,this.setOverwriteColorForPartColors(t,e,this._userPartMultiplyColors,this._userMultiplyColors)}setOverwriteColorForPartScreenColors(t,e){this._userPartScreenColors[t].isOverwritten=e,this.setOverwriteColorForPartColors(t,e,this._userPartScreenColors,this._userScreenColors)}getDrawableCulling(t){if(this.getOverwriteFlagForModelCullings()||this.getOverwriteFlagForDrawableCullings(t))return this._userCullings[t].isCulling;const e=this._model.drawables.constantFlags;return!Live2DCubismCore.Utils.hasIsDoubleSidedBit(e[t])}setDrawableCulling(t,e){this._userCullings[t].isCulling=e}getOverwriteFlagForModelCullings(){return this._isOverwrittenCullings}setOverwriteFlagForModelCullings(t){this._isOverwrittenCullings=t}getOverwriteFlagForDrawableCullings(t){return this._userCullings[t].isOverwritten}setOverwriteFlagForDrawableCullings(t,e){this._userCullings[t].isOverwritten=e}getModelOapcity(){return this._modelOpacity}setModelOapcity(t){this._modelOpacity=t}getModel(){return this._model}getPartIndex(t){let e;const i=this._model.parts.count;for(e=0;e<i;++e)if(t==this._partIds[e])return e;return t in this._notExistPartId?this._notExistPartId[t]:(e=i+this._notExistPartId.length,this._notExistPartId[t]=e,this._notExistPartOpacities[e]=0,e)}getPartId(t){return this._model.parts.ids[t]}getPartCount(){return this._model.parts.count}setPartOpacityByIndex(t,e){t in this._notExistPartOpacities?this._notExistPartOpacities[t]=e:(T(0<=t&&t<this.getPartCount()),this._partOpacities[t]=e)}setPartOpacityById(t,e){const i=this.getPartIndex(t);i<0||this.setPartOpacityByIndex(i,e)}getPartOpacityByIndex(t){return t in this._notExistPartOpacities?this._notExistPartOpacities[t]:(T(0<=t&&t<this.getPartCount()),this._partOpacities[t])}getPartOpacityById(t){const e=this.getPartIndex(t);return e<0?0:this.getPartOpacityByIndex(e)}getParameterIndex(t){let e;const i=this._model.parameters.count;for(e=0;e<i;++e)if(t==this._parameterIds[e])return e;return t in this._notExistParameterId?this._notExistParameterId[t]:(e=this._model.parameters.count+Object.keys(this._notExistParameterId).length,this._notExistParameterId[t]=e,this._notExistParameterValues[e]=0,e)}getParameterCount(){return this._model.parameters.count}getParameterType(t){return this._model.parameters.types[t]}getParameterMaximumValue(t){return this._model.parameters.maximumValues[t]}getParameterMinimumValue(t){return this._model.parameters.minimumValues[t]}getParameterDefaultValue(t){return this._model.parameters.defaultValues[t]}getParameterValueByIndex(t){return t in this._notExistParameterValues?this._notExistParameterValues[t]:(T(0<=t&&t<this.getParameterCount()),this._parameterValues[t])}getParameterValueById(t){const e=this.getParameterIndex(t);return this.getParameterValueByIndex(e)}setParameterValueByIndex(t,e,i=1){t in this._notExistParameterValues?this._notExistParameterValues[t]=1==i?e:this._notExistParameterValues[t]*(1-i)+e*i:(T(0<=t&&t<this.getParameterCount()),this._model.parameters.maximumValues[t]<e&&(e=this._model.parameters.maximumValues[t]),this._model.parameters.minimumValues[t]>e&&(e=this._model.parameters.minimumValues[t]),this._parameterValues[t]=1==i?e:this._parameterValues[t]=this._parameterValues[t]*(1-i)+e*i)}setParameterValueById(t,e,i=1){const s=this.getParameterIndex(t);this.setParameterValueByIndex(s,e,i)}addParameterValueByIndex(t,e,i=1){this.setParameterValueByIndex(t,this.getParameterValueByIndex(t)+e*i)}addParameterValueById(t,e,i=1){const s=this.getParameterIndex(t);this.addParameterValueByIndex(s,e,i)}multiplyParameterValueById(t,e,i=1){const s=this.getParameterIndex(t);this.multiplyParameterValueByIndex(s,e,i)}multiplyParameterValueByIndex(t,e,i=1){this.setParameterValueByIndex(t,this.getParameterValueByIndex(t)*(1+(e-1)*i))}getDrawableIds(){return this._drawableIds.slice()}getDrawableIndex(t){const e=this._model.drawables.count;for(let i=0;i<e;++i)if(this._drawableIds[i]==t)return i;return-1}getDrawableCount(){return this._model.drawables.count}getDrawableId(t){return this._model.drawables.ids[t]}getDrawableRenderOrders(){return this._model.drawables.renderOrders}getDrawableTextureIndices(t){return this.getDrawableTextureIndex(t)}getDrawableTextureIndex(t){return this._model.drawables.textureIndices[t]}getDrawableDynamicFlagVertexPositionsDidChange(t){const e=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasVertexPositionsDidChangeBit(e[t])}getDrawableVertexIndexCount(t){return this._model.drawables.indexCounts[t]}getDrawableVertexCount(t){return this._model.drawables.vertexCounts[t]}getDrawableVertices(t){return this.getDrawableVertexPositions(t)}getDrawableVertexIndices(t){return this._model.drawables.indices[t]}getDrawableVertexPositions(t){return this._model.drawables.vertexPositions[t]}getDrawableVertexUvs(t){return this._model.drawables.vertexUvs[t]}getDrawableOpacity(t){return this._model.drawables.opacities[t]}getDrawableMultiplyColor(t){const e=this._model.drawables.multiplyColors,i=4*t,s=new y;return s.R=e[i],s.G=e[i+1],s.B=e[i+2],s.A=e[i+3],s}getDrawableScreenColor(t){const e=this._model.drawables.screenColors,i=4*t,s=new y;return s.R=e[i],s.G=e[i+1],s.B=e[i+2],s.A=e[i+3],s}getDrawableParentPartIndex(t){return this._model.drawables.parentPartIndices[t]}getDrawableBlendMode(t){const e=this._model.drawables.constantFlags;return Live2DCubismCore.Utils.hasBlendAdditiveBit(e[t])?C.CubismBlendMode_Additive:Live2DCubismCore.Utils.hasBlendMultiplicativeBit(e[t])?C.CubismBlendMode_Multiplicative:C.CubismBlendMode_Normal}getDrawableInvertedMaskBit(t){const e=this._model.drawables.constantFlags;return Live2DCubismCore.Utils.hasIsInvertedMaskBit(e[t])}getDrawableMasks(){return this._model.drawables.masks}getDrawableMaskCounts(){return this._model.drawables.maskCounts}isUsingMasking(){for(let t=0;t<this._model.drawables.count;++t)if(!(this._model.drawables.maskCounts[t]<=0))return!0;return!1}getDrawableDynamicFlagIsVisible(t){const e=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasIsVisibleBit(e[t])}getDrawableDynamicFlagVisibilityDidChange(t){const e=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasVisibilityDidChangeBit(e[t])}getDrawableDynamicFlagOpacityDidChange(t){const e=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasOpacityDidChangeBit(e[t])}getDrawableDynamicFlagRenderOrderDidChange(t){const e=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasRenderOrderDidChangeBit(e[t])}getDrawableDynamicFlagBlendColorDidChange(t){const e=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasBlendColorDidChangeBit(e[t])}loadParameters(){let t=this._model.parameters.count;const e=this._savedParameters.length;t>e&&(t=e);for(let i=0;i<t;++i)this._parameterValues[i]=this._savedParameters[i]}initialize(){this._parameterValues=this._model.parameters.values,this._partOpacities=this._model.parts.opacities,this._parameterMaximumValues=this._model.parameters.maximumValues,this._parameterMinimumValues=this._model.parameters.minimumValues;{const t=this._model.parameters.ids,e=this._model.parameters.count;for(let i=0;i<e;++i)this._parameterIds.push(t[i])}const t=this._model.parts.count;{const e=this._model.parts.ids;for(let i=0;i<t;++i)this._partIds.push(e[i])}{const e=this._model.drawables.ids,i=this._model.drawables.count,s=new R(!1,!1);for(let r=0;r<t;++r){const t=new y(1,1,1,1),e=new y(0,0,0,1),i=new B(!1,t),s=new B(!1,e);this._userPartMultiplyColors.push(i),this._userPartScreenColors.push(s),this._partChildDrawables.push([])}for(let t=0;t<i;++t){const i=new y(1,1,1,1),r=new y(0,0,0,1),o=new D(!1,i),a=new D(!1,r);this._drawableIds.push(e[t]),this._userMultiplyColors.push(o),this._userScreenColors.push(a),this._userCullings.push(s);const n=this.getDrawableParentPartIndex(t);n>=0&&this._partChildDrawables[n].push(t)}}}constructor(t){this._model=t,this._savedParameters=[],this._parameterIds=[],this._drawableIds=[],this._partIds=[],this._isOverwrittenModelMultiplyColors=!1,this._isOverwrittenModelScreenColors=!1,this._isOverwrittenCullings=!1,this._modelOpacity=1,this._userMultiplyColors=[],this._userScreenColors=[],this._userCullings=[],this._userPartMultiplyColors=[],this._userPartScreenColors=[],this._partChildDrawables=[],this._notExistPartId={},this._notExistParameterId={},this._notExistParameterValues={},this._notExistPartOpacities={},this.initialize()}release(){this._model.release(),this._model=void 0}}class k{static create(t,e){if(e){if(!this.hasMocConsistency(t))throw new Error("Inconsistent MOC3.")}const i=Live2DCubismCore.Moc.fromArrayBuffer(t);if(i){const e=new k(i);return e._mocVersion=Live2DCubismCore.Version.csmGetMocVersion(i,t),e}throw new Error("Failed to CubismMoc.create().")}createModel(){let t;const e=Live2DCubismCore.Model.fromMoc(this._moc);if(e)return t=new O(e),++this._modelCount,t;throw new Error("Unknown error")}deleteModel(t){null!=t&&--this._modelCount}constructor(t){this._moc=t,this._modelCount=0,this._mocVersion=0}release(){this._moc._release(),this._moc=void 0}getLatestMocVersion(){return Live2DCubismCore.Version.csmGetLatestMocVersion()}getMocVersion(){return this._mocVersion}static hasMocConsistency(t){return 1===Live2DCubismCore.Moc.prototype.hasMocConsistency(t)}}class U{constructor(t,e){this._json=t}release(){this._json=void 0}getUserDataCount(){return this._json.Meta.UserDataCount}getTotalUserDataSize(){return this._json.Meta.TotalUserDataSize}getUserDataTargetType(t){return this._json.UserData[t].Target}getUserDataId(t){return this._json.UserData[t].Id}getUserDataValue(t){return this._json.UserData[t].Value}}class V{static create(t,e){const i=new V;return i.parseUserData(t,e),i}getArtMeshUserDatas(){return this._artMeshUserDataNode}parseUserData(t,e){const i=new U(t,e),s=i.getUserDataCount();for(let r=0;r<s;r++){const t={targetId:i.getUserDataId(r),targetType:i.getUserDataTargetType(r),value:i.getUserDataValue(r)};this._userDataNodes.push(t),"ArtMesh"==t.targetType&&this._artMeshUserDataNode.push(t)}i.release()}constructor(){this._userDataNodes=[],this._artMeshUserDataNode=[]}release(){this._userDataNodes=null}}class N{constructor(){this._fadeInSeconds=-1,this._fadeOutSeconds=-1,this._weight=1,this._offsetSeconds=0,this._firedEventValues=[]}release(){this._weight=0}updateParameters(t,e,i){if(!e.isAvailable()||e.isFinished())return;if(!e.isStarted()){e.setIsStarted(!0),e.setStartTime(i-this._offsetSeconds),e.setFadeInStartTime(i);const t=this.getDuration();e.getEndTime()<0&&e.setEndTime(t<=0?-1:e.getStartTime()+t)}let s=this._weight;s=s*(0==this._fadeInSeconds?1:_.getEasingSine((i-e.getFadeInStartTime())/this._fadeInSeconds))*(0==this._fadeOutSeconds||e.getEndTime()<0?1:_.getEasingSine((e.getEndTime()-i)/this._fadeOutSeconds)),e.setState(i,s),T(0<=s&&s<=1),this.doUpdateParameters(t,i,s,e),e.getEndTime()>0&&e.getEndTime()<i&&e.setIsFinished(!0)}setFadeInTime(t){this._fadeInSeconds=t}setFadeOutTime(t){this._fadeOutSeconds=t}getFadeOutTime(){return this._fadeOutSeconds}getFadeInTime(){return this._fadeInSeconds}setWeight(t){this._weight=t}getWeight(){return this._weight}getDuration(){return-1}getLoopDuration(){return-1}setOffsetTime(t){this._offsetSeconds=t}getFiredEvent(t,e){return this._firedEventValues}setFinishedMotionHandler(t){this._onFinishedMotion=t}getFinishedMotionHandler(){return this._onFinishedMotion}isExistModelOpacity(){return!1}getModelOpacityIndex(){return-1}getModelOpacityId(t){}getModelOpacityValue(){return 1}}class G extends N{static create(t){const e=new G;return e.parse(t),e}doUpdateParameters(t,e,i,s){for(let r=0;r<this._parameters.length;++r){const e=this._parameters[r];switch(e.blendType){case 0:t.addParameterValueById(e.parameterId,e.value,i);break;case 1:t.multiplyParameterValueById(e.parameterId,e.value,i);break;case 2:t.setParameterValueById(e.parameterId,e.value,i)}}}parse(t){this.setFadeInTime(null!=t.FadeInTime?t.FadeInTime:1),this.setFadeOutTime(null!=t.FadeOutTime?t.FadeOutTime:1);const e=(t.Parameters||[]).length;for(let i=0;i<e;++i){const e=t.Parameters[i],s=e.Id,r=e.Value;let o;o=e.Blend&&"Add"!==e.Blend?"Multiply"===e.Blend?1:"Overwrite"===e.Blend?2:0:0;const a={parameterId:s,blendType:o,value:r};this._parameters.push(a)}}constructor(){super(),this._parameters=[]}}var X,z=(t=>(t[t.ExpressionBlendType_Add=0]="ExpressionBlendType_Add",t[t.ExpressionBlendType_Multiply=1]="ExpressionBlendType_Multiply",t[t.ExpressionBlendType_Overwrite=2]="ExpressionBlendType_Overwrite",t))(z||{});t.CubismConfig=void 0,(X=t.CubismConfig||(t.CubismConfig={})).supportMoreMaskDivisions=!0,X.setOpacityFromMotion=!1;var j=(t=>(t[t.CubismMotionCurveTarget_Model=0]="CubismMotionCurveTarget_Model",t[t.CubismMotionCurveTarget_Parameter=1]="CubismMotionCurveTarget_Parameter",t[t.CubismMotionCurveTarget_PartOpacity=2]="CubismMotionCurveTarget_PartOpacity",t))(j||{}),H=(t=>(t[t.CubismMotionSegmentType_Linear=0]="CubismMotionSegmentType_Linear",t[t.CubismMotionSegmentType_Bezier=1]="CubismMotionSegmentType_Bezier",t[t.CubismMotionSegmentType_Stepped=2]="CubismMotionSegmentType_Stepped",t[t.CubismMotionSegmentType_InverseStepped=3]="CubismMotionSegmentType_InverseStepped",t))(H||{});class W{constructor(t=0,e=0){this.time=t,this.value=e}}class Y{constructor(){this.basePointIndex=0,this.segmentType=0}}class q{constructor(){this.id="",this.type=0,this.segmentCount=0,this.baseSegmentIndex=0,this.fadeInTime=0,this.fadeOutTime=0}}class ${constructor(){this.fireTime=0,this.value=""}}class J{constructor(){this.duration=0,this.loop=!1,this.curveCount=0,this.eventCount=0,this.fps=0,this.curves=[],this.segments=[],this.points=[],this.events=[]}}class Z{constructor(t){this._json=t}release(){this._json=void 0}getMotionDuration(){return this._json.Meta.Duration}isMotionLoop(){return this._json.Meta.Loop||!1}getEvaluationOptionFlag(t){return 0==t&&!!this._json.Meta.AreBeziersRestricted}getMotionCurveCount(){return this._json.Meta.CurveCount}getMotionFps(){return this._json.Meta.Fps}getMotionTotalSegmentCount(){return this._json.Meta.TotalSegmentCount}getMotionTotalPointCount(){return this._json.Meta.TotalPointCount}getMotionFadeInTime(){return this._json.Meta.FadeInTime}getMotionFadeOutTime(){return this._json.Meta.FadeOutTime}getMotionCurveTarget(t){return this._json.Curves[t].Target}getMotionCurveId(t){return this._json.Curves[t].Id}getMotionCurveFadeInTime(t){return this._json.Curves[t].FadeInTime}getMotionCurveFadeOutTime(t){return this._json.Curves[t].FadeOutTime}getMotionCurveSegmentCount(t){return this._json.Curves[t].Segments.length}getMotionCurveSegment(t,e){return this._json.Curves[t].Segments[e]}getEventCount(){return this._json.Meta.UserDataCount||0}getTotalEventValueSize(){return this._json.Meta.TotalUserDataSize}getEventTime(t){return this._json.UserData[t].Time}getEventValue(t){return this._json.UserData[t].Value}}var Q=(t=>(t[t.EvaluationOptionFlag_AreBeziersRistricted=0]="EvaluationOptionFlag_AreBeziersRistricted",t))(Q||{});const K="Opacity";function tt(t,e,i){const s=new W;return s.time=t.time+(e.time-t.time)*i,s.value=t.value+(e.value-t.value)*i,s}function et(t,e){let i=(e-t[0].time)/(t[1].time-t[0].time);return i<0&&(i=0),t[0].value+(t[1].value-t[0].value)*i}function it(t,e){let i=(e-t[0].time)/(t[3].time-t[0].time);i<0&&(i=0);const s=tt(t[0],t[1],i),r=tt(t[1],t[2],i),o=tt(t[2],t[3],i),a=tt(s,r,i),n=tt(r,o,i);return tt(a,n,i).value}function st(t,e){const i=e,s=t[0].time,r=t[3].time,o=t[1].time,a=t[2].time,n=r-3*a+3*o-s,l=3*a-6*o+3*s,h=3*o-3*s,u=s-i,d=_.cardanoAlgorithmForBezier(n,l,h,u),c=tt(t[0],t[1],d),g=tt(t[1],t[2],d),m=tt(t[2],t[3],d),p=tt(c,g,d),f=tt(g,m,d);return tt(p,f,d).value}function rt(t,e){return t[0].value}function ot(t,e){return t[1].value}function at(t,e,i){const s=t.curves[e];let r=-1;const o=s.baseSegmentIndex+s.segmentCount;let a=0;for(let l=s.baseSegmentIndex;l<o;++l)if(a=t.segments[l].basePointIndex+(t.segments[l].segmentType==H.CubismMotionSegmentType_Bezier?3:1),t.points[a].time>i){r=l;break}if(-1==r)return t.points[a].value;const n=t.segments[r];return n.evaluate(t.points.slice(n.basePointIndex),i)}class nt extends N{constructor(){super(),this._eyeBlinkParameterIds=[],this._lipSyncParameterIds=[],this._sourceFrameRate=30,this._loopDurationSeconds=-1,this._isLoop=!1,this._isLoopFadeIn=!0,this._lastWeight=0,this._modelOpacity=1}static create(t,e){const i=new nt;return i.parse(t),i._sourceFrameRate=i._motionData.fps,i._loopDurationSeconds=i._motionData.duration,i._onFinishedMotion=e,i}doUpdateParameters(e,i,s,r){null==this._modelCurveIdEyeBlink&&(this._modelCurveIdEyeBlink="EyeBlink"),null==this._modelCurveIdLipSync&&(this._modelCurveIdLipSync="LipSync"),null==this._modelCurveIdOpacity&&(this._modelCurveIdOpacity=K);let o=i-r.getStartTime();o<0&&(o=0);let a=Number.MAX_VALUE,n=Number.MAX_VALUE;const l=64;let h=0,u=0;this._eyeBlinkParameterIds.length>l&&I("too many eye blink targets : {0}",this._eyeBlinkParameterIds.length),this._lipSyncParameterIds.length>l&&I("too many lip sync targets : {0}",this._lipSyncParameterIds.length);const d=this._fadeInSeconds<=0?1:_.getEasingSine((i-r.getFadeInStartTime())/this._fadeInSeconds),c=this._fadeOutSeconds<=0||r.getEndTime()<0?1:_.getEasingSine((r.getEndTime()-i)/this._fadeOutSeconds);let g,m,p,f=o;if(this._isLoop)for(;f>this._motionData.duration;)f-=this._motionData.duration;const x=this._motionData.curves;for(m=0;m<this._motionData.curveCount&&x[m].type==j.CubismMotionCurveTarget_Model;++m)g=at(this._motionData,m,f),x[m].id==this._modelCurveIdEyeBlink?n=g:x[m].id==this._modelCurveIdLipSync?a=g:x[m].id==this._modelCurveIdOpacity&&(this._modelOpacity=g,e.setModelOapcity(this.getModelOpacityValue()));for(;m<this._motionData.curveCount&&x[m].type==j.CubismMotionCurveTarget_Parameter;++m){if(p=e.getParameterIndex(x[m].id),-1==p)continue;const t=e.getParameterValueByIndex(p);if(g=at(this._motionData,m,f),n!=Number.MAX_VALUE)for(let e=0;e<this._eyeBlinkParameterIds.length&&e<l;++e)if(this._eyeBlinkParameterIds[e]==x[m].id){g*=n,u|=1<<e;break}if(a!=Number.MAX_VALUE)for(let e=0;e<this._lipSyncParameterIds.length&&e<l;++e)if(this._lipSyncParameterIds[e]==x[m].id){g+=a,h|=1<<e;break}let o;if(x[m].fadeInTime<0&&x[m].fadeOutTime<0)o=t+(g-t)*s;else{let e,s;e=x[m].fadeInTime<0?d:0==x[m].fadeInTime?1:_.getEasingSine((i-r.getFadeInStartTime())/x[m].fadeInTime),s=x[m].fadeOutTime<0?c:0==x[m].fadeOutTime||r.getEndTime()<0?1:_.getEasingSine((r.getEndTime()-i)/x[m].fadeOutTime);o=t+(g-t)*(this._weight*e*s)}e.setParameterValueByIndex(p,o,1)}if(n!=Number.MAX_VALUE)for(let t=0;t<this._eyeBlinkParameterIds.length&&t<l;++t){const i=e.getParameterValueById(this._eyeBlinkParameterIds[t]);if(u>>t&1)continue;const r=i+(n-i)*s;e.setParameterValueById(this._eyeBlinkParameterIds[t],r)}if(a!=Number.MAX_VALUE)for(let t=0;t<this._lipSyncParameterIds.length&&t<l;++t){const i=e.getParameterValueById(this._lipSyncParameterIds[t]);if(h>>t&1)continue;const r=i+(a-i)*s;e.setParameterValueById(this._lipSyncParameterIds[t],r)}for(;m<this._motionData.curveCount&&x[m].type==j.CubismMotionCurveTarget_PartOpacity;++m)if(g=at(this._motionData,m,f),t.CubismConfig.setOpacityFromMotion)e.setPartOpacityById(x[m].id,g);else{if(p=e.getParameterIndex(x[m].id),-1==p)continue;e.setParameterValueByIndex(p,g)}o>=this._motionData.duration&&(this._isLoop?(r.setStartTime(i),this._isLoopFadeIn&&r.setFadeInStartTime(i)):(this._onFinishedMotion&&this._onFinishedMotion(this),r.setIsFinished(!0))),this._lastWeight=s}setIsLoop(t){this._isLoop=t}isLoop(){return this._isLoop}setIsLoopFadeIn(t){this._isLoopFadeIn=t}isLoopFadeIn(){return this._isLoopFadeIn}getDuration(){return this._isLoop?-1:this._loopDurationSeconds}getLoopDuration(){return this._loopDurationSeconds}setParameterFadeInTime(t,e){const i=this._motionData.curves;for(let s=0;s<this._motionData.curveCount;++s)if(t==i[s].id)return void(i[s].fadeInTime=e)}setParameterFadeOutTime(t,e){const i=this._motionData.curves;for(let s=0;s<this._motionData.curveCount;++s)if(t==i[s].id)return void(i[s].fadeOutTime=e)}getParameterFadeInTime(t){const e=this._motionData.curves;for(let i=0;i<this._motionData.curveCount;++i)if(t==e[i].id)return e[i].fadeInTime;return-1}getParameterFadeOutTime(t){const e=this._motionData.curves;for(let i=0;i<this._motionData.curveCount;++i)if(t==e[i].id)return e[i].fadeOutTime;return-1}setEffectIds(t,e){this._eyeBlinkParameterIds=t,this._lipSyncParameterIds=e}release(){this._motionData=void 0}parse(t){this._motionData=new J;const e=new Z(t);this._motionData.duration=e.getMotionDuration(),this._motionData.loop=e.isMotionLoop(),this._motionData.curveCount=e.getMotionCurveCount(),this._motionData.fps=e.getMotionFps(),this._motionData.eventCount=e.getEventCount();const i=e.getEvaluationOptionFlag(Q.EvaluationOptionFlag_AreBeziersRistricted),s=e.getMotionFadeInTime(),r=e.getMotionFadeOutTime();this._fadeInSeconds=void 0!==s?s<0?1:s:1,this._fadeOutSeconds=void 0!==r?r<0?1:r:1,this._motionData.curves=Array.from({length:this._motionData.curveCount}).map((()=>new q)),this._motionData.segments=Array.from({length:e.getMotionTotalSegmentCount()}).map((()=>new Y)),this._motionData.events=Array.from({length:this._motionData.eventCount}).map((()=>new $)),this._motionData.points=[];let o=0,a=0;for(let n=0;n<this._motionData.curveCount;++n){const t=this._motionData.curves[n];switch(e.getMotionCurveTarget(n)){case"Model":t.type=j.CubismMotionCurveTarget_Model;break;case"Parameter":t.type=j.CubismMotionCurveTarget_Parameter;break;case"PartOpacity":t.type=j.CubismMotionCurveTarget_PartOpacity;break;default:L('Warning : Unable to get segment type from Curve! The number of "CurveCount" may be incorrect!')}t.id=e.getMotionCurveId(n),t.baseSegmentIndex=a;const s=e.getMotionCurveFadeInTime(n),r=e.getMotionCurveFadeOutTime(n);t.fadeInTime=void 0!==s?s:-1,t.fadeOutTime=void 0!==r?r:-1;for(let l=0;l<e.getMotionCurveSegmentCount(n);){0==l?(this._motionData.segments[a].basePointIndex=o,this._motionData.points[o]=new W(e.getMotionCurveSegment(n,l),e.getMotionCurveSegment(n,l+1)),o+=1,l+=2):this._motionData.segments[a].basePointIndex=o-1;switch(e.getMotionCurveSegment(n,l)){case H.CubismMotionSegmentType_Linear:this._motionData.segments[a].segmentType=H.CubismMotionSegmentType_Linear,this._motionData.segments[a].evaluate=et,this._motionData.points[o]=new W(e.getMotionCurveSegment(n,l+1),e.getMotionCurveSegment(n,l+2)),o+=1,l+=3;break;case H.CubismMotionSegmentType_Bezier:this._motionData.segments[a].segmentType=H.CubismMotionSegmentType_Bezier,this._motionData.segments[a].evaluate=i?it:st,this._motionData.points[o]=new W(e.getMotionCurveSegment(n,l+1),e.getMotionCurveSegment(n,l+2)),this._motionData.points[o+1]=new W(e.getMotionCurveSegment(n,l+3),e.getMotionCurveSegment(n,l+4)),this._motionData.points[o+2]=new W(e.getMotionCurveSegment(n,l+5),e.getMotionCurveSegment(n,l+6)),o+=3,l+=7;break;case H.CubismMotionSegmentType_Stepped:this._motionData.segments[a].segmentType=H.CubismMotionSegmentType_Stepped,this._motionData.segments[a].evaluate=rt,this._motionData.points[o]=new W(e.getMotionCurveSegment(n,l+1),e.getMotionCurveSegment(n,l+2)),o+=1,l+=3;break;case H.CubismMotionSegmentType_InverseStepped:this._motionData.segments[a].segmentType=H.CubismMotionSegmentType_InverseStepped,this._motionData.segments[a].evaluate=ot,this._motionData.points[o]=new W(e.getMotionCurveSegment(n,l+1),e.getMotionCurveSegment(n,l+2)),o+=1,l+=3;break;default:T(0)}++t.segmentCount,++a}this._motionData.curves.push(t)}for(let n=0;n<e.getEventCount();++n)this._motionData.events[n].fireTime=e.getEventTime(n),this._motionData.events[n].value=e.getEventValue(n);e.release()}getFiredEvent(t,e){this._firedEventValues.length=0;for(let i=0;i<this._motionData.eventCount;++i)this._motionData.events[i].fireTime>t&&this._motionData.events[i].fireTime<=e&&this._firedEventValues.push(this._motionData.events[i].value);return this._firedEventValues}isExistModelOpacity(){for(let t=0;t<this._motionData.curveCount;t++){const e=this._motionData.curves[t];if(e.type==j.CubismMotionCurveTarget_Model&&e.id===K)return!0}return!1}getModelOpacityIndex(){if(this.isExistModelOpacity())for(let t=0;t<this._motionData.curveCount;t++){const e=this._motionData.curves[t];if(e.type==j.CubismMotionCurveTarget_Model&&e.id===K)return t}return-1}getModelOpacityId(t){if(-1!=t){const e=this._motionData.curves[t];if(e.type==j.CubismMotionCurveTarget_Model&&e.id===K)return e.id}}getModelOpacityValue(){return this._modelOpacity}}class lt{constructor(){this._autoDelete=!1,this._available=!0,this._finished=!1,this._started=!1,this._startTimeSeconds=-1,this._fadeInStartTimeSeconds=0,this._endTimeSeconds=-1,this._stateTimeSeconds=0,this._stateWeight=0,this._lastEventCheckSeconds=0,this._motionQueueEntryHandle=this,this._fadeOutSeconds=0,this._isTriggeredFadeOut=!1}release(){this._autoDelete&&this._motion&&this._motion.release()}setFadeOut(t){this._fadeOutSeconds=t,this._isTriggeredFadeOut=!0}startFadeOut(t,e){const i=e+t;this._isTriggeredFadeOut=!0,(this._endTimeSeconds<0||i<this._endTimeSeconds)&&(this._endTimeSeconds=i)}isFinished(){return this._finished}isStarted(){return this._started}getStartTime(){return this._startTimeSeconds}getFadeInStartTime(){return this._fadeInStartTimeSeconds}getEndTime(){return this._endTimeSeconds}setStartTime(t){this._startTimeSeconds=t}setFadeInStartTime(t){this._fadeInStartTimeSeconds=t}setEndTime(t){this._endTimeSeconds=t}setIsFinished(t){this._finished=t}setIsStarted(t){this._started=t}isAvailable(){return this._available}setIsAvailable(t){this._available=t}setState(t,e){this._stateTimeSeconds=t,this._stateWeight=e}getStateTime(){return this._stateTimeSeconds}getStateWeight(){return this._stateWeight}getLastCheckEventSeconds(){return this._lastEventCheckSeconds}setLastCheckEventSeconds(t){this._lastEventCheckSeconds=t}isTriggeredFadeOut(){return this._isTriggeredFadeOut}getFadeOutSeconds(){return this._fadeOutSeconds}}class ht{constructor(){this._userTimeSeconds=0,this._eventCustomData=null,this._motions=[]}release(){for(let t=0;t<this._motions.length;++t)this._motions[t]&&this._motions[t].release();this._motions=void 0}startMotion(t,e,i){if(null==t)return ut;let s;for(let r=0;r<this._motions.length;++r)s=this._motions[r],null!=s&&s.setFadeOut(s._motion.getFadeOutTime());return s=new lt,s._autoDelete=e,s._motion=t,this._motions.push(s),s._motionQueueEntryHandle}isFinished(){let t=0;for(;t<this._motions.length;){const e=this._motions[t];if(null==e){this._motions.splice(t,1);continue}if(null!=e._motion){if(!e.isFinished())return!1;t++}else e.release(),this._motions.splice(t,1)}return!0}isFinishedByHandle(t){for(let e=0;e<this._motions.length;e++){const i=this._motions[e];if(null!=i&&(i._motionQueueEntryHandle==t&&!i.isFinished()))return!1}return!0}stopAllMotions(){for(let t=0;t<this._motions.length;t++){const e=this._motions[t];null!=e&&e.release()}this._motions=[]}getCubismMotionQueueEntry(t){return this._motions.find((e=>null!=e&&e._motionQueueEntryHandle==t))}setEventCallback(t,e=null){this._eventCallBack=t,this._eventCustomData=e}doUpdateMotion(t,e){let i=!1,s=0;for(;s<this._motions.length;){const r=this._motions[s];if(null==r){this._motions.splice(s,1);continue}const o=r._motion;if(null==o){r.release(),this._motions.splice(s,1);continue}o.updateParameters(t,r,e),i=!0;const a=o.getFiredEvent(r.getLastCheckEventSeconds()-r.getStartTime(),e-r.getStartTime());for(let t=0;t<a.length;++t)this._eventCallBack(this,a[t],this._eventCustomData);r.setLastCheckEventSeconds(e),r.isFinished()?(r.release(),this._motions.splice(s,1)):(r.isTriggeredFadeOut()&&r.startFadeOut(r.getFadeOutSeconds(),e),s++)}return i}}const ut=-1;var dt=(t=>(t[t.CubismPhysicsTargetType_Parameter=0]="CubismPhysicsTargetType_Parameter",t))(dt||{}),ct=(t=>(t[t.CubismPhysicsSource_X=0]="CubismPhysicsSource_X",t[t.CubismPhysicsSource_Y=1]="CubismPhysicsSource_Y",t[t.CubismPhysicsSource_Angle=2]="CubismPhysicsSource_Angle",t))(ct||{});class gt{constructor(){this.initialPosition=new m(0,0),this.position=new m(0,0),this.lastPosition=new m(0,0),this.lastGravity=new m(0,0),this.force=new m(0,0),this.velocity=new m(0,0)}}class mt{constructor(){this.normalizationPosition={},this.normalizationAngle={}}}class pt{constructor(){this.source={}}}class _t{constructor(){this.destination={},this.translationScale=new m(0,0)}}class ft{constructor(){this.settings=[],this.inputs=[],this.outputs=[],this.particles=[],this.gravity=new m(0,0),this.wind=new m(0,0),this.fps=0}}class xt{constructor(t){this._json=t}release(){this._json=void 0}getGravity(){const t=new m(0,0);return t.x=this._json.Meta.EffectiveForces.Gravity.X,t.y=this._json.Meta.EffectiveForces.Gravity.Y,t}getWind(){const t=new m(0,0);return t.x=this._json.Meta.EffectiveForces.Wind.X,t.y=this._json.Meta.EffectiveForces.Wind.Y,t}getFps(){return this._json.Meta.Fps||0}getSubRigCount(){return this._json.Meta.PhysicsSettingCount}getTotalInputCount(){return this._json.Meta.TotalInputCount}getTotalOutputCount(){return this._json.Meta.TotalOutputCount}getVertexCount(){return this._json.Meta.VertexCount}getNormalizationPositionMinimumValue(t){return this._json.PhysicsSettings[t].Normalization.Position.Minimum}getNormalizationPositionMaximumValue(t){return this._json.PhysicsSettings[t].Normalization.Position.Maximum}getNormalizationPositionDefaultValue(t){return this._json.PhysicsSettings[t].Normalization.Position.Default}getNormalizationAngleMinimumValue(t){return this._json.PhysicsSettings[t].Normalization.Angle.Minimum}getNormalizationAngleMaximumValue(t){return this._json.PhysicsSettings[t].Normalization.Angle.Maximum}getNormalizationAngleDefaultValue(t){return this._json.PhysicsSettings[t].Normalization.Angle.Default}getInputCount(t){return this._json.PhysicsSettings[t].Input.length}getInputWeight(t,e){return this._json.PhysicsSettings[t].Input[e].Weight}getInputReflect(t,e){return this._json.PhysicsSettings[t].Input[e].Reflect}getInputType(t,e){return this._json.PhysicsSettings[t].Input[e].Type}getInputSourceId(t,e){return this._json.PhysicsSettings[t].Input[e].Source.Id}getOutputCount(t){return this._json.PhysicsSettings[t].Output.length}getOutputVertexIndex(t,e){return this._json.PhysicsSettings[t].Output[e].VertexIndex}getOutputAngleScale(t,e){return this._json.PhysicsSettings[t].Output[e].Scale}getOutputWeight(t,e){return this._json.PhysicsSettings[t].Output[e].Weight}getOutputDestinationId(t,e){return this._json.PhysicsSettings[t].Output[e].Destination.Id}getOutputType(t,e){return this._json.PhysicsSettings[t].Output[e].Type}getOutputReflect(t,e){return this._json.PhysicsSettings[t].Output[e].Reflect}getParticleCount(t){return this._json.PhysicsSettings[t].Vertices.length}getParticleMobility(t,e){return this._json.PhysicsSettings[t].Vertices[e].Mobility}getParticleDelay(t,e){return this._json.PhysicsSettings[t].Vertices[e].Delay}getParticleAcceleration(t,e){return this._json.PhysicsSettings[t].Vertices[e].Acceleration}getParticleRadius(t,e){return this._json.PhysicsSettings[t].Vertices[e].Radius}getParticlePosition(t,e){const i=new m(0,0);return i.x=this._json.PhysicsSettings[t].Vertices[e].Position.X,i.y=this._json.PhysicsSettings[t].Vertices[e].Position.Y,i}}const Ct="Angle";class yt{static create(t){const e=new yt;return e.parse(t),e._physicsRig.gravity.y=0,e}static delete(t){null!=t&&t.release()}parse(t){this._physicsRig=new ft;const e=new xt(t);this._physicsRig.gravity=e.getGravity(),this._physicsRig.wind=e.getWind(),this._physicsRig.subRigCount=e.getSubRigCount(),this._physicsRig.fps=e.getFps(),this._currentRigOutputs=[],this._previousRigOutputs=[];let i=0,s=0,r=0;for(let o=0;o<this._physicsRig.subRigCount;++o){const t=new mt;t.normalizationPosition.minimum=e.getNormalizationPositionMinimumValue(o),t.normalizationPosition.maximum=e.getNormalizationPositionMaximumValue(o),t.normalizationPosition.defalut=e.getNormalizationPositionDefaultValue(o),t.normalizationAngle.minimum=e.getNormalizationAngleMinimumValue(o),t.normalizationAngle.maximum=e.getNormalizationAngleMaximumValue(o),t.normalizationAngle.defalut=e.getNormalizationAngleDefaultValue(o),t.inputCount=e.getInputCount(o),t.baseInputIndex=i,i+=t.inputCount;for(let i=0;i<t.inputCount;++i){const t=new pt;switch(t.sourceParameterIndex=-1,t.weight=e.getInputWeight(o,i),t.reflect=e.getInputReflect(o,i),e.getInputType(o,i)){case"X":t.type=ct.CubismPhysicsSource_X,t.getNormalizedParameterValue=Pt;break;case"Y":t.type=ct.CubismPhysicsSource_Y,t.getNormalizedParameterValue=bt;break;case Ct:t.type=ct.CubismPhysicsSource_Angle,t.getNormalizedParameterValue=St}t.source.targetType=dt.CubismPhysicsTargetType_Parameter,t.source.id=e.getInputSourceId(o,i),this._physicsRig.inputs.push(t)}t.outputCount=e.getOutputCount(o),t.baseOutputIndex=s;const a=new vt,n=new vt;for(let i=0;i<t.outputCount;++i){a.outputs[i]=0,n.outputs[i]=0;let t=this._physicsRig.outputs[s+i];switch(t||(t=new _t,this._physicsRig.outputs[s+i]=t),t.destinationParameterIndex=-1,t.vertexIndex=e.getOutputVertexIndex(o,i),t.angleScale=e.getOutputAngleScale(o,i),t.weight=e.getOutputWeight(o,i),t.destination.targetType=dt.CubismPhysicsTargetType_Parameter,t.destination.id=e.getOutputDestinationId(o,i),e.getOutputType(o,i)){case"X":t.type=ct.CubismPhysicsSource_X,t.getValue=wt,t.getScale=Lt;break;case"Y":t.type=ct.CubismPhysicsSource_Y,t.getValue=Tt,t.getScale=Ft;break;case Ct:t.type=ct.CubismPhysicsSource_Angle,t.getValue=It,t.getScale=At}t.reflect=e.getOutputReflect(o,i)}this._currentRigOutputs.push(a),this._previousRigOutputs.push(n),s+=t.outputCount,t.particleCount=e.getParticleCount(o),t.baseParticleIndex=r,r+=t.particleCount;for(let i=0;i<t.particleCount;++i){const t=new gt;t.mobility=e.getParticleMobility(o,i),t.delay=e.getParticleDelay(o,i),t.acceleration=e.getParticleAcceleration(o,i),t.radius=e.getParticleRadius(o,i),t.position=e.getParticlePosition(o,i),this._physicsRig.particles.push(t)}this._physicsRig.settings.push(t)}this.initialize(),e.release()}stabilization(t){var e,i,s,r;let o,a,n,l;const h=new m;let u,d,c,g,p,f,x,C;p=t.getModel().parameters.values,f=t.getModel().parameters.maximumValues,x=t.getModel().parameters.minimumValues,C=t.getModel().parameters.defaultValues,(null!=(i=null==(e=this._parameterCaches)?void 0:e.length)?i:0)<t.getParameterCount()&&(this._parameterCaches=new Float32Array(t.getParameterCount())),(null!=(r=null==(s=this._parameterInputCaches)?void 0:s.length)?r:0)<t.getParameterCount()&&(this._parameterInputCaches=new Float32Array(t.getParameterCount()));for(let m=0;m<t.getParameterCount();++m)this._parameterCaches[m]=p[m],this._parameterInputCaches[m]=p[m];for(let y=0;y<this._physicsRig.subRigCount;++y){o={angle:0},h.x=0,h.y=0,u=this._physicsRig.settings[y],d=this._physicsRig.inputs.slice(u.baseInputIndex),c=this._physicsRig.outputs.slice(u.baseOutputIndex),g=this._physicsRig.particles.slice(u.baseParticleIndex);for(let e=0;e<u.inputCount;++e)a=d[e].weight/100,-1==d[e].sourceParameterIndex&&(d[e].sourceParameterIndex=t.getParameterIndex(d[e].source.id)),d[e].getNormalizedParameterValue(h,o,p[d[e].sourceParameterIndex],x[d[e].sourceParameterIndex],f[d[e].sourceParameterIndex],C[d[e].sourceParameterIndex],u.normalizationPosition,u.normalizationAngle,d[e].reflect,a),this._parameterCaches[d[e].sourceParameterIndex]=p[d[e].sourceParameterIndex];n=_.degreesToRadian(-o.angle),h.x=h.x*_.cos(n)-h.y*_.sin(n),h.y=h.x*_.sin(n)+h.y*_.cos(n),Bt(g,u.particleCount,h,o.angle,this._options.wind,.001*u.normalizationPosition.maximum);for(let e=0;e<u.outputCount;++e){const i=c[e].vertexIndex;if(-1==c[e].destinationParameterIndex&&(c[e].destinationParameterIndex=t.getParameterIndex(c[e].destination.id)),i<1||i>=u.particleCount)continue;let s=new m;s=g[i].position.substract(g[i-1].position),l=c[e].getValue(s,g,i,c[e].reflect,this._options.gravity),this._currentRigOutputs[y].outputs[e]=l,this._previousRigOutputs[y].outputs[e]=l;const r=c[e].destinationParameterIndex,o=!Float32Array.prototype.slice&&"subarray"in Float32Array.prototype?JSON.parse(JSON.stringify(p.subarray(r))):p.slice(r);Rt(o,x[r],f[r],l,c[e]);for(let t=r,e=0;t<this._parameterCaches.length;t++,e++)p[t]=this._parameterCaches[t]=o[e]}}}evaluate(t,e){var i,s,r,o;let a,n,l,h;const u=new m;let d,c,g,p,f,x,C,y,M;if(0>=e)return;if(this._currentRemainTime+=e,this._currentRemainTime>5&&(this._currentRemainTime=0),f=t.getModel().parameters.values,x=t.getModel().parameters.maximumValues,C=t.getModel().parameters.minimumValues,y=t.getModel().parameters.defaultValues,(null!=(s=null==(i=this._parameterCaches)?void 0:i.length)?s:0)<t.getParameterCount()&&(this._parameterCaches=new Float32Array(t.getParameterCount())),(null!=(o=null==(r=this._parameterInputCaches)?void 0:r.length)?o:0)<t.getParameterCount()){this._parameterInputCaches=new Float32Array(t.getParameterCount());for(let e=0;e<t.getParameterCount();++e)this._parameterInputCaches[e]=f[e]}for(M=this._physicsRig.fps>0?1/this._physicsRig.fps:e;this._currentRemainTime>=M;){for(let t=0;t<this._physicsRig.subRigCount;++t){d=this._physicsRig.settings[t],g=this._physicsRig.outputs.slice(d.baseOutputIndex);for(let e=0;e<d.outputCount;++e)this._previousRigOutputs[t].outputs[e]=this._currentRigOutputs[t].outputs[e]}const e=M/this._currentRemainTime;for(let i=0;i<t.getParameterCount();++i)this._parameterCaches[i]=this._parameterInputCaches[i]*(1-e)+f[i]*e,this._parameterInputCaches[i]=this._parameterCaches[i];for(let i=0;i<this._physicsRig.subRigCount;++i){a={angle:0},u.x=0,u.y=0,d=this._physicsRig.settings[i],c=this._physicsRig.inputs.slice(d.baseInputIndex),g=this._physicsRig.outputs.slice(d.baseOutputIndex),p=this._physicsRig.particles.slice(d.baseParticleIndex);for(let e=0;e<d.inputCount;++e)n=c[e].weight/100,-1==c[e].sourceParameterIndex&&(c[e].sourceParameterIndex=t.getParameterIndex(c[e].source.id)),c[e].getNormalizedParameterValue(u,a,this._parameterCaches[c[e].sourceParameterIndex],C[c[e].sourceParameterIndex],x[c[e].sourceParameterIndex],y[c[e].sourceParameterIndex],d.normalizationPosition,d.normalizationAngle,c[e].reflect,n);l=_.degreesToRadian(-a.angle),u.x=u.x*_.cos(l)-u.y*_.sin(l),u.y=u.x*_.sin(l)+u.y*_.cos(l),Dt(p,d.particleCount,u,a.angle,this._options.wind,.001*d.normalizationPosition.maximum,M,5);for(let e=0;e<d.outputCount;++e){const s=g[e].vertexIndex;if(-1==g[e].destinationParameterIndex&&(g[e].destinationParameterIndex=t.getParameterIndex(g[e].destination.id)),s<1||s>=d.particleCount)continue;const r=new m;r.x=p[s].position.x-p[s-1].position.x,r.y=p[s].position.y-p[s-1].position.y,h=g[e].getValue(r,p,s,g[e].reflect,this._options.gravity),this._currentRigOutputs[i].outputs[e]=h;const o=g[e].destinationParameterIndex,a=!Float32Array.prototype.slice&&"subarray"in Float32Array.prototype?JSON.parse(JSON.stringify(this._parameterCaches.subarray(o))):this._parameterCaches.slice(o);Rt(a,C[o],x[o],h,g[e]);for(let t=o,e=0;t<this._parameterCaches.length;t++,e++)this._parameterCaches[t]=a[e]}}this._currentRemainTime-=M}const v=this._currentRemainTime/M;this.interpolate(t,v)}interpolate(t,e){let i,s,r,o,a;r=t.getModel().parameters.values,o=t.getModel().parameters.maximumValues,a=t.getModel().parameters.minimumValues;for(let n=0;n<this._physicsRig.subRigCount;++n){s=this._physicsRig.settings[n],i=this._physicsRig.outputs.slice(s.baseOutputIndex);for(let t=0;t<s.outputCount;++t){if(-1==i[t].destinationParameterIndex)continue;const s=i[t].destinationParameterIndex,l=!Float32Array.prototype.slice&&"subarray"in Float32Array.prototype?JSON.parse(JSON.stringify(r.subarray(s))):r.slice(s);Rt(l,a[s],o[s],this._previousRigOutputs[n].outputs[t]*(1-e)+this._currentRigOutputs[n].outputs[t]*e,i[t]);for(let t=s,e=0;t<r.length;t++,e++)r[t]=l[e]}}}setOptions(t){this._options=t}getOption(){return this._options}constructor(){this._options=new Mt,this._options.gravity.y=-1,this._options.gravity.x=0,this._options.wind.x=0,this._options.wind.y=0,this._currentRigOutputs=[],this._previousRigOutputs=[],this._currentRemainTime=0}release(){this._physicsRig=void 0}initialize(){let t,e,i;for(let s=0;s<this._physicsRig.subRigCount;++s){e=this._physicsRig.settings[s],t=this._physicsRig.particles.slice(e.baseParticleIndex),t[0].initialPosition=new m(0,0),t[0].lastPosition=new m(t[0].initialPosition.x,t[0].initialPosition.y),t[0].lastGravity=new m(0,-1),t[0].lastGravity.y*=-1,t[0].velocity=new m(0,0),t[0].force=new m(0,0);for(let s=1;s<e.particleCount;++s)i=new m(0,0),i.y=t[s].radius,t[s].initialPosition=new m(t[s-1].initialPosition.x+i.x,t[s-1].initialPosition.y+i.y),t[s].position=new m(t[s].initialPosition.x,t[s].initialPosition.y),t[s].lastPosition=new m(t[s].initialPosition.x,t[s].initialPosition.y),t[s].lastGravity=new m(0,-1),t[s].lastGravity.y*=-1,t[s].velocity=new m(0,0),t[s].force=new m(0,0)}}}class Mt{constructor(){this.gravity=new m(0,0),this.wind=new m(0,0)}}class vt{constructor(){this.outputs=[]}}function Pt(t,e,i,s,r,o,a,n,l,h){t.x+=Ot(i,s,r,o,a.minimum,a.maximum,a.defalut,l)*h}function bt(t,e,i,s,r,o,a,n,l,h){t.y+=Ot(i,s,r,o,a.minimum,a.maximum,a.defalut,l)*h}function St(t,e,i,s,r,o,a,n,l,h){e.angle+=Ot(i,s,r,o,n.minimum,n.maximum,n.defalut,l)*h}function wt(t,e,i,s,r){let o=t.x;return s&&(o*=-1),o}function Tt(t,e,i,s,r){let o=t.y;return s&&(o*=-1),o}function It(t,e,i,s,r){let o;return r=i>=2?e[i-1].position.substract(e[i-2].position):r.multiplyByScaler(-1),o=_.directionToRadian(r,t),s&&(o*=-1),o}function Et(t,e){return Math.min(t,e)+function(t,e){return Math.abs(Math.max(t,e)-Math.min(t,e))}(t,e)/2}function Lt(t,e){return t.x}function Ft(t,e){return t.y}function At(t,e){return e}function Dt(t,e,i,s,r,o,a,n){let l,h,u,d,c=new m(0,0),g=new m(0,0),p=new m(0,0),f=new m(0,0);t[0].position=new m(i.x,i.y),l=_.degreesToRadian(s),d=_.radianToDirection(l),d.normalize();for(let x=1;x<e;++x)t[x].force=d.multiplyByScaler(t[x].acceleration).add(r),t[x].lastPosition=new m(t[x].position.x,t[x].position.y),h=t[x].delay*a*30,c=t[x].position.substract(t[x-1].position),u=_.directionToRadian(t[x].lastGravity,d)/n,c.x=_.cos(u)*c.x-c.y*_.sin(u),c.y=_.sin(u)*c.x+c.y*_.cos(u),t[x].position=t[x-1].position.add(c),g=t[x].velocity.multiplyByScaler(h),p=t[x].force.multiplyByScaler(h).multiplyByScaler(h),t[x].position=t[x].position.add(g).add(p),f=t[x].position.substract(t[x-1].position),f.normalize(),t[x].position=t[x-1].position.add(f.multiplyByScaler(t[x].radius)),_.abs(t[x].position.x)<o&&(t[x].position.x=0),0!=h&&(t[x].velocity=t[x].position.substract(t[x].lastPosition),t[x].velocity=t[x].velocity.divisionByScalar(h),t[x].velocity=t[x].velocity.multiplyByScaler(t[x].mobility)),t[x].force=new m(0,0),t[x].lastGravity=new m(d.x,d.y)}function Bt(t,e,i,s,r,o){let a,n,l=new m(0,0);t[0].position=new m(i.x,i.y),a=_.degreesToRadian(s),n=_.radianToDirection(a),n.normalize();for(let h=1;h<e;++h)t[h].force=n.multiplyByScaler(t[h].acceleration).add(r),t[h].lastPosition=new m(t[h].position.x,t[h].position.y),t[h].velocity=new m(0,0),l=t[h].force,l.normalize(),l=l.multiplyByScaler(t[h].radius),t[h].position=t[h-1].position.add(l),_.abs(t[h].position.x)<o&&(t[h].position.x=0),t[h].force=new m(0,0),t[h].lastGravity=new m(n.x,n.y)}function Rt(t,e,i,s,r){let o,a,n;o=r.getScale(r.translationScale,r.angleScale),a=s*o,a<e?(a<r.valueBelowMinimum&&(r.valueBelowMinimum=a),a=e):a>i&&(a>r.valueExceededMaximum&&(r.valueExceededMaximum=a),a=i),n=r.weight/100,n>=1||(a=t[0]*(1-n)+a*n),t[0]=a}function Ot(t,e,i,s,r,o,a,n){let l=0;const h=_.max(i,e);h<t&&(t=h);const u=_.min(i,e);u>t&&(t=u);const d=_.min(r,o),c=_.max(r,o),g=a,m=Et(u,h),p=t-m;switch(Math.sign(p)){case 1:{const t=h-m;0!=t&&(l=p*((c-g)/t),l+=g);break}case-1:{const t=u-m;0!=t&&(l=p*((d-g)/t),l+=g);break}case 0:l=g}return n?l:-1*l}class kt{constructor(t=0,e=0,i=0,s=0){this.x=t,this.y=e,this.width=i,this.height=s}getCenterX(){return this.x+.5*this.width}getCenterY(){return this.y+.5*this.height}getRight(){return this.x+this.width}getBottom(){return this.y+this.height}setRect(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height}expand(t,e){this.x-=t,this.y-=e,this.width+=2*t,this.height+=2*e}}let Ut,Vt,Nt;class Gt{getChannelFlagAsColor(t){return this._channelColors[t]}getMaskRenderTexture(){if(this._maskTexture&&null!=this._maskTexture.textures)this._maskTexture.frameNo=this._currentFrameNo;else{this._maskRenderTextures=[],this._maskColorBuffers=[];const t=this._clippingMaskBufferSize;for(let e=0;e<this._renderTextureCount;e++)this._maskColorBuffers.push(this.gl.createTexture()),this.gl.bindTexture(this.gl.TEXTURE_2D,this._maskColorBuffers[e]),this.gl.texImage2D(this.gl.TEXTURE_2D,0,this.gl.RGBA,t,t,0,this.gl.RGBA,this.gl.UNSIGNED_BYTE,null),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_WRAP_S,this.gl.CLAMP_TO_EDGE),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_WRAP_T,this.gl.CLAMP_TO_EDGE),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_MIN_FILTER,this.gl.LINEAR),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_MAG_FILTER,this.gl.LINEAR),this.gl.bindTexture(this.gl.TEXTURE_2D,null),this._maskRenderTextures.push(this.gl.createFramebuffer()),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,this._maskRenderTextures[e]),this.gl.framebufferTexture2D(this.gl.FRAMEBUFFER,this.gl.COLOR_ATTACHMENT0,this.gl.TEXTURE_2D,this._maskColorBuffers[e],0);this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,Nt),this._maskTexture=new Xt(this._currentFrameNo,this._maskRenderTextures)}return this._maskTexture.textures}setGL(t){this.gl=t}calcClippedDrawTotalBounds(t,e){let i=Number.MAX_VALUE,s=Number.MAX_VALUE,r=Number.MIN_VALUE,o=Number.MIN_VALUE;const a=e._clippedDrawableIndexList.length;for(let n=0;n<a;n++){const a=e._clippedDrawableIndexList[n],l=t.getDrawableVertexCount(a),h=t.getDrawableVertices(a);let u=Number.MAX_VALUE,d=Number.MAX_VALUE,c=-Number.MAX_VALUE,g=-Number.MAX_VALUE;const m=l*b.vertexStep;for(let t=b.vertexOffset;t<m;t+=b.vertexStep){const e=h[t],i=h[t+1];e<u&&(u=e),e>c&&(c=e),i<d&&(d=i),i>g&&(g=i)}if(u!=Number.MAX_VALUE)if(u<i&&(i=u),d<s&&(s=d),c>r&&(r=c),g>o&&(o=g),i==Number.MAX_VALUE)e._allClippedDrawRect.x=0,e._allClippedDrawRect.y=0,e._allClippedDrawRect.width=0,e._allClippedDrawRect.height=0,e._isUsing=!1;else{e._isUsing=!0;const t=r-i,a=o-s;e._allClippedDrawRect.x=i,e._allClippedDrawRect.y=s,e._allClippedDrawRect.width=t,e._allClippedDrawRect.height=a}}}constructor(){this._currentMaskRenderTexture=null,this._currentFrameNo=0,this._renderTextureCount=0,this._clippingMaskBufferSize=256,this._clippingContextListForMask=[],this._clippingContextListForDraw=[],this._channelColors=[],this._tmpBoundsOnModel=new kt,this._tmpMatrix=new f,this._tmpMatrixForMask=new f,this._tmpMatrixForDraw=new f;let t=new y;t.R=1,t.G=0,t.B=0,t.A=0,this._channelColors.push(t),t=new y,t.R=0,t.G=1,t.B=0,t.A=0,this._channelColors.push(t),t=new y,t.R=0,t.G=0,t.B=1,t.A=0,this._channelColors.push(t),t=new y,t.R=0,t.G=0,t.B=0,t.A=1,this._channelColors.push(t)}release(){var t;const e=this;for(let i=0;i<this._clippingContextListForMask.length;i++)this._clippingContextListForMask[i]&&(null==(t=this._clippingContextListForMask[i])||t.release());if(e._clippingContextListForMask=void 0,e._clippingContextListForDraw=void 0,this._maskTexture){for(let t=0;t<this._maskTexture.textures.length;t++)this.gl.deleteFramebuffer(this._maskTexture.textures[t]);this._maskTexture=void 0}if(e._channelColors=void 0,this._maskColorBuffers)for(let i=0;i<this._maskColorBuffers.length;i++)this.gl.deleteTexture(this._maskColorBuffers[i]);this._maskColorBuffers=void 0,this._maskRenderTextures=void 0,this._clearedFrameBufferflags=void 0}initialize(t,e,i,s,r){r%1!=0&&(L("The number of render textures must be specified as an integer. The decimal point is rounded down and corrected to an integer."),r=~~r),r<1&&L("The number of render textures must be an integer greater than or equal to 1. Set the number of render textures to 1."),this._renderTextureCount=r<1?1:r,this._clearedFrameBufferflags=[];for(let o=0;o<e;o++){if(s[o]<=0){this._clippingContextListForDraw.push(null);continue}let t=this.findSameClip(i[o],s[o]);null==t&&(t=new zt(this,i[o],s[o]),this._clippingContextListForMask.push(t)),t.addClippedDrawable(o),this._clippingContextListForDraw.push(t)}}setupClippingContext(t,e){this._currentFrameNo++;let i=0;for(let s=0;s<this._clippingContextListForMask.length;s++){const e=this._clippingContextListForMask[s];this.calcClippedDrawTotalBounds(t,e),e._isUsing&&i++}if(i>0){this.setupLayoutBounds(e.isUsingHighPrecisionMask()?0:i),e.isUsingHighPrecisionMask()||(this.gl.viewport(0,0,this._clippingMaskBufferSize,this._clippingMaskBufferSize),this._currentMaskRenderTexture=this.getMaskRenderTexture()[0],e.preDraw(),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,this._currentMaskRenderTexture)),this._clearedFrameBufferflags||(this._clearedFrameBufferflags=[]);for(let t=0;t<this._renderTextureCount;t++)this._clearedFrameBufferflags[t]=!1;for(let i=0;i<this._clippingContextListForMask.length;i++){const s=this._clippingContextListForMask[i],r=s._allClippedDrawRect,o=s._layoutBounds,a=.05;let n=0,l=0;const h=this.getMaskRenderTexture()[s._bufferIndex];if(this._currentMaskRenderTexture==h||e.isUsingHighPrecisionMask()||(this._currentMaskRenderTexture=h,e.preDraw(),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,this._currentMaskRenderTexture)),e.isUsingHighPrecisionMask()){const e=t.getPixelsPerUnit(),i=s.getClippingManager()._clippingMaskBufferSize,h=o.width*i,u=o.height*i;this._tmpBoundsOnModel.setRect(r),this._tmpBoundsOnModel.width*e>h?(this._tmpBoundsOnModel.expand(r.width*a,0),n=o.width/this._tmpBoundsOnModel.width):n=e/h,this._tmpBoundsOnModel.height*e>u?(this._tmpBoundsOnModel.expand(0,r.height*a),l=o.height/this._tmpBoundsOnModel.height):l=e/u}else this._tmpBoundsOnModel.setRect(r),this._tmpBoundsOnModel.expand(r.width*a,r.height*a),n=o.width/this._tmpBoundsOnModel.width,l=o.height/this._tmpBoundsOnModel.height;if(this._tmpMatrix.loadIdentity(),this._tmpMatrix.translateRelative(-1,-1),this._tmpMatrix.scaleRelative(2,2),this._tmpMatrix.translateRelative(o.x,o.y),this._tmpMatrix.scaleRelative(n,l),this._tmpMatrix.translateRelative(-this._tmpBoundsOnModel.x,-this._tmpBoundsOnModel.y),this._tmpMatrixForMask.setMatrix(this._tmpMatrix.getArray()),this._tmpMatrix.loadIdentity(),this._tmpMatrix.translateRelative(o.x,o.y),this._tmpMatrix.scaleRelative(n,l),this._tmpMatrix.translateRelative(-this._tmpBoundsOnModel.x,-this._tmpBoundsOnModel.y),this._tmpMatrixForDraw.setMatrix(this._tmpMatrix.getArray()),s._matrixForMask.setMatrix(this._tmpMatrixForMask.getArray()),s._matrixForDraw.setMatrix(this._tmpMatrixForDraw.getArray()),!e.isUsingHighPrecisionMask()){const i=s._clippingIdCount;for(let r=0;r<i;r++){const i=s._clippingIdList[r];t.getDrawableDynamicFlagVertexPositionsDidChange(i)&&(e.setIsCulling(0!=t.getDrawableCulling(i)),this._clearedFrameBufferflags[s._bufferIndex]||(this.gl.clearColor(1,1,1,1),this.gl.clear(this.gl.COLOR_BUFFER_BIT),this._clearedFrameBufferflags[s._bufferIndex]=!0),e.setClippingContextBufferForMask(s),e.drawMesh(t.getDrawableTextureIndex(i),t.getDrawableVertexIndexCount(i),t.getDrawableVertexCount(i),t.getDrawableVertexIndices(i),t.getDrawableVertices(i),t.getDrawableVertexUvs(i),t.getMultiplyColor(i),t.getScreenColor(i),t.getDrawableOpacity(i),C.CubismBlendMode_Normal,!1))}}}e.isUsingHighPrecisionMask()||(this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,Nt),e.setClippingContextBufferForMask(null),this.gl.viewport(Vt[0],Vt[1],Vt[2],Vt[3]))}}findSameClip(t,e){for(let i=0;i<this._clippingContextListForMask.length;i++){const s=this._clippingContextListForMask[i],r=s._clippingIdCount;if(r!=e)continue;let o=0;for(let e=0;e<r;e++){const i=s._clippingIdList[e];for(let e=0;e<r;e++)if(t[e]==i){o++;break}}if(o==r)return s}return null}setupLayoutBounds(e){const i=this._renderTextureCount<=1?36:32*this._renderTextureCount;if(e<=0||e>i){e>i&&F("not supported mask count : {0}\n[Details] render texture count : {1}, mask count : {2}",e-i,this._renderTextureCount,e);for(let t=0;t<this._clippingContextListForMask.length;t++){const e=this._clippingContextListForMask[t];e._layoutChannelNo=0,e._layoutBounds.x=0,e._layoutBounds.y=0,e._layoutBounds.width=1,e._layoutBounds.height=1,e._bufferIndex=0}return}const s=this._renderTextureCount<=1?9:8;let r=e/this._renderTextureCount,o=e%this._renderTextureCount;r=~~r,o=~~o;let a=r/4,n=r%4;a=~~a,n=~~n;let l=0;for(let h=0;h<this._renderTextureCount;h++)for(let r=0;r<4;r++){let u=a+(r<n?1:0);if(u<s&&r==(n+1>=4?0:n+1)&&(u+=h<o?1:0),0==u);else if(1==u){const t=this._clippingContextListForMask[l++];t._layoutChannelNo=r,t._layoutBounds.x=0,t._layoutBounds.y=0,t._layoutBounds.width=1,t._layoutBounds.height=1,t._bufferIndex=h}else if(2==u)for(let t=0;t<u;t++){let e=t%2;e=~~e;const i=this._clippingContextListForMask[l++];i._layoutChannelNo=r,i._layoutBounds.x=.5*e,i._layoutBounds.y=0,i._layoutBounds.width=.5,i._layoutBounds.height=1,i._bufferIndex=h}else if(u<=4)for(let t=0;t<u;t++){let e=t%2,i=t/2;e=~~e,i=~~i;const s=this._clippingContextListForMask[l++];s._layoutChannelNo=r,s._layoutBounds.x=.5*e,s._layoutBounds.y=.5*i,s._layoutBounds.width=.5,s._layoutBounds.height=.5,s._bufferIndex=h}else if(u<=s)for(let t=0;t<u;t++){let e=t%3,i=t/3;e=~~e,i=~~i;const s=this._clippingContextListForMask[l++];s._layoutChannelNo=r,s._layoutBounds.x=e/3,s._layoutBounds.y=i/3,s._layoutBounds.width=1/3,s._layoutBounds.height=1/3,s._bufferIndex=h}else if(t.CubismConfig.supportMoreMaskDivisions&&u<=16)for(let t=0;t<u;t++){let e=t%4,i=t/4;e=~~e,i=~~i;const s=this._clippingContextListForMask[l++];s._layoutChannelNo=r,s._layoutBounds.x=e/4,s._layoutBounds.y=i/4,s._layoutBounds.width=1/4,s._layoutBounds.height=1/4,s._bufferIndex=h}else{F("not supported mask count : {0}\n[Details] render texture count : {1}, mask count : {2}",e-i,this._renderTextureCount,e);for(let t=0;t<u;t++){const t=this._clippingContextListForMask[l++];t._layoutChannelNo=0,t._layoutBounds.x=0,t._layoutBounds.y=0,t._layoutBounds.width=1,t._layoutBounds.height=1,t._bufferIndex=0}}}}getColorBuffer(){return this._maskColorBuffers}getClippingContextListForDraw(){return this._clippingContextListForDraw}getClippingMaskCount(){return this._clippingContextListForMask.length}setClippingMaskBufferSize(t){this._clippingMaskBufferSize=t}getClippingMaskBufferSize(){return this._clippingMaskBufferSize}getRenderTextureCount(){return this._renderTextureCount}}class Xt{constructor(t,e){this.frameNo=t,this.textures=e}}class zt{constructor(t,e,i){this._isUsing=!1,this._owner=t,this._clippingIdList=e,this._clippingIdCount=i,this._allClippedDrawRect=new kt,this._layoutBounds=new kt,this._clippedDrawableIndexList=[],this._matrixForMask=new f,this._matrixForDraw=new f,this._bufferIndex=0}release(){const t=this;t._layoutBounds=void 0,t._allClippedDrawRect=void 0,t._clippedDrawableIndexList=void 0}addClippedDrawable(t){this._clippedDrawableIndexList.push(t)}getClippingManager(){return this._owner}setGl(t){this._owner.setGL(t)}}class jt{setGlEnable(t,e){e?this.gl.enable(t):this.gl.disable(t)}setGlEnableVertexAttribArray(t,e){e?this.gl.enableVertexAttribArray(t):this.gl.disableVertexAttribArray(t)}save(){null!=this.gl?(this._lastArrayBufferBinding=this.gl.getParameter(this.gl.ARRAY_BUFFER_BINDING),this._lastArrayBufferBinding=this.gl.getParameter(this.gl.ELEMENT_ARRAY_BUFFER_BINDING),this._lastProgram=this.gl.getParameter(this.gl.CURRENT_PROGRAM),this._lastActiveTexture=this.gl.getParameter(this.gl.ACTIVE_TEXTURE),this.gl.activeTexture(this.gl.TEXTURE1),this._lastTexture1Binding2D=this.gl.getParameter(this.gl.TEXTURE_BINDING_2D),this.gl.activeTexture(this.gl.TEXTURE0),this._lastTexture0Binding2D=this.gl.getParameter(this.gl.TEXTURE_BINDING_2D),this._lastVertexAttribArrayEnabled[0]=this.gl.getVertexAttrib(0,this.gl.VERTEX_ATTRIB_ARRAY_ENABLED),this._lastVertexAttribArrayEnabled[1]=this.gl.getVertexAttrib(1,this.gl.VERTEX_ATTRIB_ARRAY_ENABLED),this._lastVertexAttribArrayEnabled[2]=this.gl.getVertexAttrib(2,this.gl.VERTEX_ATTRIB_ARRAY_ENABLED),this._lastVertexAttribArrayEnabled[3]=this.gl.getVertexAttrib(3,this.gl.VERTEX_ATTRIB_ARRAY_ENABLED),this._lastScissorTest=this.gl.isEnabled(this.gl.SCISSOR_TEST),this._lastStencilTest=this.gl.isEnabled(this.gl.STENCIL_TEST),this._lastDepthTest=this.gl.isEnabled(this.gl.DEPTH_TEST),this._lastCullFace=this.gl.isEnabled(this.gl.CULL_FACE),this._lastBlend=this.gl.isEnabled(this.gl.BLEND),this._lastFrontFace=this.gl.getParameter(this.gl.FRONT_FACE),this._lastColorMask=this.gl.getParameter(this.gl.COLOR_WRITEMASK),this._lastBlending[0]=this.gl.getParameter(this.gl.BLEND_SRC_RGB),this._lastBlending[1]=this.gl.getParameter(this.gl.BLEND_DST_RGB),this._lastBlending[2]=this.gl.getParameter(this.gl.BLEND_SRC_ALPHA),this._lastBlending[3]=this.gl.getParameter(this.gl.BLEND_DST_ALPHA),this._lastFBO=this.gl.getParameter(this.gl.FRAMEBUFFER_BINDING),this._lastViewport=this.gl.getParameter(this.gl.VIEWPORT)):F("'gl' is null. WebGLRenderingContext is required.\nPlease call 'CubimRenderer_WebGL.startUp' function.")}restore(){null!=this.gl?(this.gl.useProgram(this._lastProgram),this.setGlEnableVertexAttribArray(0,this._lastVertexAttribArrayEnabled[0]),this.setGlEnableVertexAttribArray(1,this._lastVertexAttribArrayEnabled[1]),this.setGlEnableVertexAttribArray(2,this._lastVertexAttribArrayEnabled[2]),this.setGlEnableVertexAttribArray(3,this._lastVertexAttribArrayEnabled[3]),this.setGlEnable(this.gl.SCISSOR_TEST,this._lastScissorTest),this.setGlEnable(this.gl.STENCIL_TEST,this._lastStencilTest),this.setGlEnable(this.gl.DEPTH_TEST,this._lastDepthTest),this.setGlEnable(this.gl.CULL_FACE,this._lastCullFace),this.setGlEnable(this.gl.BLEND,this._lastBlend),this.gl.frontFace(this._lastFrontFace),this.gl.colorMask(this._lastColorMask[0],this._lastColorMask[1],this._lastColorMask[2],this._lastColorMask[3]),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this._lastArrayBufferBinding),this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,this._lastElementArrayBufferBinding),this.gl.activeTexture(this.gl.TEXTURE1),this.gl.bindTexture(this.gl.TEXTURE_2D,this._lastTexture1Binding2D),this.gl.activeTexture(this.gl.TEXTURE0),this.gl.bindTexture(this.gl.TEXTURE_2D,this._lastTexture0Binding2D),this.gl.activeTexture(this._lastActiveTexture),this.gl.blendFuncSeparate(this._lastBlending[0],this._lastBlending[1],this._lastBlending[2],this._lastBlending[3])):F("'gl' is null. WebGLRenderingContext is required.\nPlease call 'CubimRenderer_WebGL.startUp' function.")}setGl(t){this.gl=t}constructor(){this._lastVertexAttribArrayEnabled=new Array(4),this._lastColorMask=new Array(4),this._lastBlending=new Array(4),this._lastViewport=new Array(4)}}class Ht{static getInstance(){return null==Ut?(Ut=new Ht,Ut):Ut}static deleteInstance(){Ut&&(Ut.release(),Ut=void 0)}constructor(){this._shaderSets=[]}release(){this.releaseShaderProgram()}setupShaderProgram(t,e,i,s,r,o,a,n,l,h,u,d,c,g,m){let p,_,f,x;c||F("NoPremultipliedAlpha is not allowed"),0==this._shaderSets.length&&this.generateShaders();const y=t.getClippingContextBufferForMask();if(null!=y){const t=this._shaderSets[0];this.gl.useProgram(t.shaderProgram),this.gl.activeTexture(this.gl.TEXTURE0),this.gl.bindTexture(this.gl.TEXTURE_2D,e),this.gl.uniform1i(t.samplerTexture0Location,0),null==a.vertex&&(a.vertex=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,a.vertex),this.gl.bufferData(this.gl.ARRAY_BUFFER,s,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(t.attributePositionLocation),this.gl.vertexAttribPointer(t.attributePositionLocation,2,this.gl.FLOAT,!1,0,0),null==a.uv&&(a.uv=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,a.uv),this.gl.bufferData(this.gl.ARRAY_BUFFER,o,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(t.attributeTexCoordLocation),this.gl.vertexAttribPointer(t.attributeTexCoordLocation,2,this.gl.FLOAT,!1,0,0);const i=y._layoutChannelNo,r=y.getClippingManager().getChannelFlagAsColor(i);this.gl.uniform4f(t.uniformChannelFlagLocation,r.R,r.G,r.B,r.A),this.gl.uniformMatrix4fv(t.uniformClipMatrixLocation,!1,y._matrixForMask.getArray());const n=y._layoutBounds;this.gl.uniform4f(t.uniformBaseColorLocation,2*n.x-1,2*n.y-1,2*n.getRight()-1,2*n.getBottom()-1),this.gl.uniform4f(t.uniformMultiplyColorLocation,u.R,u.G,u.B,u.A),this.gl.uniform4f(t.uniformScreenColorLocation,d.R,d.G,d.B,d.A),p=this.gl.ZERO,_=this.gl.ONE_MINUS_SRC_COLOR,f=this.gl.ZERO,x=this.gl.ONE_MINUS_SRC_ALPHA}else{const i=t.getClippingContextBufferForDraw(),r=null!=i?m?2:1:0;let n;switch(l){case C.CubismBlendMode_Normal:default:n=this._shaderSets[1+r],p=this.gl.ONE,_=this.gl.ONE_MINUS_SRC_ALPHA,f=this.gl.ONE,x=this.gl.ONE_MINUS_SRC_ALPHA;break;case C.CubismBlendMode_Additive:n=this._shaderSets[4+r],p=this.gl.ONE,_=this.gl.ONE,f=this.gl.ZERO,x=this.gl.ONE;break;case C.CubismBlendMode_Multiplicative:n=this._shaderSets[7+r],p=this.gl.DST_COLOR,_=this.gl.ONE_MINUS_SRC_ALPHA,f=this.gl.ZERO,x=this.gl.ONE}if(this.gl.useProgram(n.shaderProgram),null==a.vertex&&(a.vertex=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,a.vertex),this.gl.bufferData(this.gl.ARRAY_BUFFER,s,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(n.attributePositionLocation),this.gl.vertexAttribPointer(n.attributePositionLocation,2,this.gl.FLOAT,!1,0,0),null==a.uv&&(a.uv=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,a.uv),this.gl.bufferData(this.gl.ARRAY_BUFFER,o,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(n.attributeTexCoordLocation),this.gl.vertexAttribPointer(n.attributeTexCoordLocation,2,this.gl.FLOAT,!1,0,0),null!=i){this.gl.activeTexture(this.gl.TEXTURE1);const e=i.getClippingManager().getColorBuffer()[t.getClippingContextBufferForDraw()._bufferIndex];this.gl.bindTexture(this.gl.TEXTURE_2D,e),this.gl.uniform1i(n.samplerTexture1Location,1),this.gl.uniformMatrix4fv(n.uniformClipMatrixLocation,!1,i._matrixForDraw.getArray());const s=i._layoutChannelNo,r=i.getClippingManager().getChannelFlagAsColor(s);this.gl.uniform4f(n.uniformChannelFlagLocation,r.R,r.G,r.B,r.A)}this.gl.activeTexture(this.gl.TEXTURE0),this.gl.bindTexture(this.gl.TEXTURE_2D,e),this.gl.uniform1i(n.samplerTexture0Location,0),this.gl.uniformMatrix4fv(n.uniformMatrixLocation,!1,g.getArray()),this.gl.uniform4f(n.uniformBaseColorLocation,h.R,h.G,h.B,h.A),this.gl.uniform4f(n.uniformMultiplyColorLocation,u.R,u.G,u.B,u.A),this.gl.uniform4f(n.uniformScreenColorLocation,d.R,d.G,d.B,d.A)}null==a.index&&(a.index=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,a.index),this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER,r,this.gl.DYNAMIC_DRAW),this.gl.blendFuncSeparate(p,_,f,x)}releaseShaderProgram(){for(let t=0;t<this._shaderSets.length;t++)this.gl.deleteProgram(this._shaderSets[t].shaderProgram),this._shaderSets[t].shaderProgram=0;this._shaderSets=[]}generateShaders(){for(let t=0;t<10;t++)this._shaderSets.push({});this._shaderSets[0].shaderProgram=this.loadShaderProgram(Yt,qt),this._shaderSets[1].shaderProgram=this.loadShaderProgram($t,Zt),this._shaderSets[2].shaderProgram=this.loadShaderProgram(Jt,Qt),this._shaderSets[3].shaderProgram=this.loadShaderProgram(Jt,Kt),this._shaderSets[4].shaderProgram=this._shaderSets[1].shaderProgram,this._shaderSets[5].shaderProgram=this._shaderSets[2].shaderProgram,this._shaderSets[6].shaderProgram=this._shaderSets[3].shaderProgram,this._shaderSets[7].shaderProgram=this._shaderSets[1].shaderProgram,this._shaderSets[8].shaderProgram=this._shaderSets[2].shaderProgram,this._shaderSets[9].shaderProgram=this._shaderSets[3].shaderProgram,this._shaderSets[0].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[0].shaderProgram,"a_position"),this._shaderSets[0].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[0].shaderProgram,"a_texCoord"),this._shaderSets[0].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[0].shaderProgram,"s_texture0"),this._shaderSets[0].uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets[0].shaderProgram,"u_clipMatrix"),this._shaderSets[0].uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets[0].shaderProgram,"u_channelFlag"),this._shaderSets[0].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[0].shaderProgram,"u_baseColor"),this._shaderSets[0].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[0].shaderProgram,"u_multiplyColor"),this._shaderSets[0].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[0].shaderProgram,"u_screenColor"),this._shaderSets[1].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[1].shaderProgram,"a_position"),this._shaderSets[1].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[1].shaderProgram,"a_texCoord"),this._shaderSets[1].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[1].shaderProgram,"s_texture0"),this._shaderSets[1].uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets[1].shaderProgram,"u_matrix"),this._shaderSets[1].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[1].shaderProgram,"u_baseColor"),this._shaderSets[1].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[1].shaderProgram,"u_multiplyColor"),this._shaderSets[1].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[1].shaderProgram,"u_screenColor"),this._shaderSets[2].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[2].shaderProgram,"a_position"),this._shaderSets[2].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[2].shaderProgram,"a_texCoord"),this._shaderSets[2].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[2].shaderProgram,"s_texture0"),this._shaderSets[2].samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets[2].shaderProgram,"s_texture1"),this._shaderSets[2].uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets[2].shaderProgram,"u_matrix"),this._shaderSets[2].uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets[2].shaderProgram,"u_clipMatrix"),this._shaderSets[2].uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets[2].shaderProgram,"u_channelFlag"),this._shaderSets[2].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[2].shaderProgram,"u_baseColor"),this._shaderSets[2].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[2].shaderProgram,"u_multiplyColor"),this._shaderSets[2].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[2].shaderProgram,"u_screenColor"),this._shaderSets[3].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[3].shaderProgram,"a_position"),this._shaderSets[3].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[3].shaderProgram,"a_texCoord"),this._shaderSets[3].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[3].shaderProgram,"s_texture0"),this._shaderSets[3].samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets[3].shaderProgram,"s_texture1"),this._shaderSets[3].uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets[3].shaderProgram,"u_matrix"),this._shaderSets[3].uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets[3].shaderProgram,"u_clipMatrix"),this._shaderSets[3].uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets[3].shaderProgram,"u_channelFlag"),this._shaderSets[3].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[3].shaderProgram,"u_baseColor"),this._shaderSets[3].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[3].shaderProgram,"u_multiplyColor"),this._shaderSets[3].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[3].shaderProgram,"u_screenColor"),this._shaderSets[4].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[4].shaderProgram,"a_position"),this._shaderSets[4].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[4].shaderProgram,"a_texCoord"),this._shaderSets[4].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[4].shaderProgram,"s_texture0"),this._shaderSets[4].uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets[4].shaderProgram,"u_matrix"),this._shaderSets[4].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[4].shaderProgram,"u_baseColor"),this._shaderSets[4].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[4].shaderProgram,"u_multiplyColor"),this._shaderSets[4].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[4].shaderProgram,"u_screenColor"),this._shaderSets[5].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[5].shaderProgram,"a_position"),this._shaderSets[5].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[5].shaderProgram,"a_texCoord"),this._shaderSets[5].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[5].shaderProgram,"s_texture0"),this._shaderSets[5].samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets[5].shaderProgram,"s_texture1"),this._shaderSets[5].uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets[5].shaderProgram,"u_matrix"),this._shaderSets[5].uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets[5].shaderProgram,"u_clipMatrix"),this._shaderSets[5].uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets[5].shaderProgram,"u_channelFlag"),this._shaderSets[5].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[5].shaderProgram,"u_baseColor"),this._shaderSets[5].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[5].shaderProgram,"u_multiplyColor"),this._shaderSets[5].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[5].shaderProgram,"u_screenColor"),this._shaderSets[6].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[6].shaderProgram,"a_position"),this._shaderSets[6].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[6].shaderProgram,"a_texCoord"),this._shaderSets[6].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[6].shaderProgram,"s_texture0"),this._shaderSets[6].samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets[6].shaderProgram,"s_texture1"),this._shaderSets[6].uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets[6].shaderProgram,"u_matrix"),this._shaderSets[6].uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets[6].shaderProgram,"u_clipMatrix"),this._shaderSets[6].uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets[6].shaderProgram,"u_channelFlag"),this._shaderSets[6].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[6].shaderProgram,"u_baseColor"),this._shaderSets[6].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[6].shaderProgram,"u_multiplyColor"),this._shaderSets[6].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[6].shaderProgram,"u_screenColor"),this._shaderSets[7].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[7].shaderProgram,"a_position"),this._shaderSets[7].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[7].shaderProgram,"a_texCoord"),this._shaderSets[7].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[7].shaderProgram,"s_texture0"),this._shaderSets[7].uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets[7].shaderProgram,"u_matrix"),this._shaderSets[7].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[7].shaderProgram,"u_baseColor"),this._shaderSets[7].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[7].shaderProgram,"u_multiplyColor"),this._shaderSets[7].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[7].shaderProgram,"u_screenColor"),this._shaderSets[8].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[8].shaderProgram,"a_position"),this._shaderSets[8].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[8].shaderProgram,"a_texCoord"),this._shaderSets[8].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[8].shaderProgram,"s_texture0"),this._shaderSets[8].samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets[8].shaderProgram,"s_texture1"),this._shaderSets[8].uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets[8].shaderProgram,"u_matrix"),this._shaderSets[8].uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets[8].shaderProgram,"u_clipMatrix"),this._shaderSets[8].uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets[8].shaderProgram,"u_channelFlag"),this._shaderSets[8].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[8].shaderProgram,"u_baseColor"),this._shaderSets[8].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[8].shaderProgram,"u_multiplyColor"),this._shaderSets[8].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[8].shaderProgram,"u_screenColor"),this._shaderSets[9].attributePositionLocation=this.gl.getAttribLocation(this._shaderSets[9].shaderProgram,"a_position"),this._shaderSets[9].attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets[9].shaderProgram,"a_texCoord"),this._shaderSets[9].samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets[9].shaderProgram,"s_texture0"),this._shaderSets[9].samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets[9].shaderProgram,"s_texture1"),this._shaderSets[9].uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets[9].shaderProgram,"u_matrix"),this._shaderSets[9].uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets[9].shaderProgram,"u_clipMatrix"),this._shaderSets[9].uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets[9].shaderProgram,"u_channelFlag"),this._shaderSets[9].uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets[9].shaderProgram,"u_baseColor"),this._shaderSets[9].uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets[9].shaderProgram,"u_multiplyColor"),this._shaderSets[9].uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets[9].shaderProgram,"u_screenColor")}loadShaderProgram(t,e){const i=this.gl.createProgram(),s=this.compileShaderSource(this.gl.VERTEX_SHADER,t);if(!s)return F("Vertex shader compile error!"),0;const r=this.compileShaderSource(this.gl.FRAGMENT_SHADER,e);if(!r)return F("Vertex shader compile error!"),0;this.gl.attachShader(i,s),this.gl.attachShader(i,r),this.gl.linkProgram(i);return this.gl.getProgramParameter(i,this.gl.LINK_STATUS)?(this.gl.deleteShader(s),this.gl.deleteShader(r),i):(F("Failed to link program: {0}",i),this.gl.deleteShader(s),this.gl.deleteShader(r),i&&this.gl.deleteProgram(i),0)}compileShaderSource(t,e){const i=e,s=this.gl.createShader(t);if(this.gl.shaderSource(s,i),this.gl.compileShader(s),!s){F("Shader compile log: {0} ",this.gl.getShaderInfoLog(s))}return this.gl.getShaderParameter(s,this.gl.COMPILE_STATUS)?s:(this.gl.deleteShader(s),null)}setGl(t){this.gl=t}}var Wt=(t=>(t[t.ShaderNames_SetupMask=0]="ShaderNames_SetupMask",t[t.ShaderNames_NormalPremultipliedAlpha=1]="ShaderNames_NormalPremultipliedAlpha",t[t.ShaderNames_NormalMaskedPremultipliedAlpha=2]="ShaderNames_NormalMaskedPremultipliedAlpha",t[t.ShaderNames_NomralMaskedInvertedPremultipliedAlpha=3]="ShaderNames_NomralMaskedInvertedPremultipliedAlpha",t[t.ShaderNames_AddPremultipliedAlpha=4]="ShaderNames_AddPremultipliedAlpha",t[t.ShaderNames_AddMaskedPremultipliedAlpha=5]="ShaderNames_AddMaskedPremultipliedAlpha",t[t.ShaderNames_AddMaskedPremultipliedAlphaInverted=6]="ShaderNames_AddMaskedPremultipliedAlphaInverted",t[t.ShaderNames_MultPremultipliedAlpha=7]="ShaderNames_MultPremultipliedAlpha",t[t.ShaderNames_MultMaskedPremultipliedAlpha=8]="ShaderNames_MultMaskedPremultipliedAlpha",t[t.ShaderNames_MultMaskedPremultipliedAlphaInverted=9]="ShaderNames_MultMaskedPremultipliedAlphaInverted",t))(Wt||{});const Yt="attribute vec4     a_position;attribute vec2     a_texCoord;varying vec2       v_texCoord;varying vec4       v_myPos;uniform mat4       u_clipMatrix;void main(){   gl_Position = u_clipMatrix * a_position;   v_myPos = u_clipMatrix * a_position;   v_texCoord = a_texCoord;   v_texCoord.y = 1.0 - v_texCoord.y;}",qt="precision mediump float;varying vec2       v_texCoord;varying vec4       v_myPos;uniform vec4       u_baseColor;uniform vec4       u_channelFlag;uniform sampler2D  s_texture0;void main(){   float isInside =        step(u_baseColor.x, v_myPos.x/v_myPos.w)       * step(u_baseColor.y, v_myPos.y/v_myPos.w)       * step(v_myPos.x/v_myPos.w, u_baseColor.z)       * step(v_myPos.y/v_myPos.w, u_baseColor.w);   gl_FragColor = u_channelFlag * texture2D(s_texture0, v_texCoord).a * isInside;}",$t="attribute vec4     a_position;attribute vec2     a_texCoord;varying vec2       v_texCoord;uniform mat4       u_matrix;void main(){   gl_Position = u_matrix * a_position;   v_texCoord = a_texCoord;   v_texCoord.y = 1.0 - v_texCoord.y;}",Jt="attribute vec4     a_position;attribute vec2     a_texCoord;varying vec2       v_texCoord;varying vec4       v_clipPos;uniform mat4       u_matrix;uniform mat4       u_clipMatrix;void main(){   gl_Position = u_matrix * a_position;   v_clipPos = u_clipMatrix * a_position;   v_texCoord = a_texCoord;   v_texCoord.y = 1.0 - v_texCoord.y;}",Zt="precision mediump float;varying vec2       v_texCoord;uniform vec4       u_baseColor;uniform sampler2D  s_texture0;uniform vec4       u_multiplyColor;uniform vec4       u_screenColor;void main(){   vec4 texColor = texture2D(s_texture0, v_texCoord);   texColor.rgb = texColor.rgb * u_multiplyColor.rgb;   texColor.rgb = (texColor.rgb + u_screenColor.rgb * texColor.a) - (texColor.rgb * u_screenColor.rgb);   vec4 color = texColor * u_baseColor;   gl_FragColor = vec4(color.rgb, color.a);}",Qt="precision mediump float;varying vec2       v_texCoord;varying vec4       v_clipPos;uniform vec4       u_baseColor;uniform vec4       u_channelFlag;uniform sampler2D  s_texture0;uniform sampler2D  s_texture1;uniform vec4       u_multiplyColor;uniform vec4       u_screenColor;void main(){   vec4 texColor = texture2D(s_texture0, v_texCoord);   texColor.rgb = texColor.rgb * u_multiplyColor.rgb;   texColor.rgb = (texColor.rgb + u_screenColor.rgb * texColor.a) - (texColor.rgb * u_screenColor.rgb);   vec4 col_formask = texColor * u_baseColor;   vec4 clipMask = (1.0 - texture2D(s_texture1, v_clipPos.xy / v_clipPos.w)) * u_channelFlag;   float maskVal = clipMask.r + clipMask.g + clipMask.b + clipMask.a;   col_formask = col_formask * maskVal;   gl_FragColor = col_formask;}",Kt="precision mediump float;varying vec2      v_texCoord;varying vec4      v_clipPos;uniform sampler2D s_texture0;uniform sampler2D s_texture1;uniform vec4      u_channelFlag;uniform vec4      u_baseColor;uniform vec4      u_multiplyColor;uniform vec4      u_screenColor;void main(){   vec4 texColor = texture2D(s_texture0, v_texCoord);   texColor.rgb = texColor.rgb * u_multiplyColor.rgb;   texColor.rgb = (texColor.rgb + u_screenColor.rgb * texColor.a) - (texColor.rgb * u_screenColor.rgb);   vec4 col_formask = texColor * u_baseColor;   vec4 clipMask = (1.0 - texture2D(s_texture1, v_clipPos.xy / v_clipPos.w)) * u_channelFlag;   float maskVal = clipMask.r + clipMask.g + clipMask.b + clipMask.a;   col_formask = col_formask * (1.0 - maskVal);   gl_FragColor = col_formask;}";class te extends x{initialize(t,e=1){t.isUsingMasking()&&(this._clippingManager=new Gt,this._clippingManager.initialize(t,t.getDrawableCount(),t.getDrawableMasks(),t.getDrawableMaskCounts(),e));for(let i=t.getDrawableCount()-1;i>=0;i--)this._sortedDrawableIndexList[i]=0;super.initialize(t)}bindTexture(t,e){this._textures[t]=e}getBindedTextures(){return this._textures}setClippingMaskBufferSize(t){if(!this._model.isUsingMasking())return;const e=this._clippingManager.getRenderTextureCount();this._clippingManager.release(),this._clippingManager=new Gt,this._clippingManager.setClippingMaskBufferSize(t),this._clippingManager.initialize(this.getModel(),this.getModel().getDrawableCount(),this.getModel().getDrawableMasks(),this.getModel().getDrawableMaskCounts(),e)}getClippingMaskBufferSize(){return this._model.isUsingMasking()?this._clippingManager.getClippingMaskBufferSize():-1}getRenderTextureCount(){return this._model.isUsingMasking()?this._clippingManager.getRenderTextureCount():-1}constructor(){super(),this._clippingContextBufferForMask=null,this._clippingContextBufferForDraw=null,this._rendererProfile=new jt,this.firstDraw=!0,this._textures={},this._sortedDrawableIndexList=[],this._bufferData={vertex:null,uv:null,index:null}}release(){var t,e,i;const s=this;this._clippingManager.release(),s._clippingManager=void 0,null==(t=this.gl)||t.deleteBuffer(this._bufferData.vertex),this._bufferData.vertex=null,null==(e=this.gl)||e.deleteBuffer(this._bufferData.uv),this._bufferData.uv=null,null==(i=this.gl)||i.deleteBuffer(this._bufferData.index),this._bufferData.index=null,s._bufferData=void 0,s._textures=void 0}doDrawModel(){if(null==this.gl)return void F("'gl' is null. WebGLRenderingContext is required.\nPlease call 'CubimRenderer_WebGL.startUp' function.");null!=this._clippingManager&&(this.preDraw(),this._clippingManager.setupClippingContext(this.getModel(),this)),this.preDraw();const t=this.getModel().getDrawableCount(),e=this.getModel().getDrawableRenderOrders();for(let i=0;i<t;++i){const t=e[i];this._sortedDrawableIndexList[t]=i}for(let i=0;i<t;++i){const t=this._sortedDrawableIndexList[i];if(!this.getModel().getDrawableDynamicFlagIsVisible(t))continue;const e=null!=this._clippingManager?this._clippingManager.getClippingContextListForDraw()[t]:null;if(null!=e&&this.isUsingHighPrecisionMask()){e._isUsing&&(this.gl.viewport(0,0,this._clippingManager.getClippingMaskBufferSize(),this._clippingManager.getClippingMaskBufferSize()),this.preDraw(),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,e.getClippingManager().getMaskRenderTexture()[e._bufferIndex]),this.gl.clearColor(1,1,1,1),this.gl.clear(this.gl.COLOR_BUFFER_BIT));{const t=e._clippingIdCount;for(let i=0;i<t;i++){const t=e._clippingIdList[i];this._model.getDrawableDynamicFlagVertexPositionsDidChange(t)&&(this.setIsCulling(0!=this._model.getDrawableCulling(t)),this.setClippingContextBufferForMask(e),this.drawMesh(this.getModel().getDrawableTextureIndex(t),this.getModel().getDrawableVertexIndexCount(t),this.getModel().getDrawableVertexCount(t),this.getModel().getDrawableVertexIndices(t),this.getModel().getDrawableVertices(t),this.getModel().getDrawableVertexUvs(t),this.getModel().getMultiplyColor(t),this.getModel().getScreenColor(t),this.getModel().getDrawableOpacity(t),C.CubismBlendMode_Normal,!1))}}this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,Nt),this.setClippingContextBufferForMask(null),this.gl.viewport(Vt[0],Vt[1],Vt[2],Vt[3]),this.preDraw()}this.setClippingContextBufferForDraw(e),this.setIsCulling(this.getModel().getDrawableCulling(t)),this.drawMesh(this.getModel().getDrawableTextureIndex(t),this.getModel().getDrawableVertexIndexCount(t),this.getModel().getDrawableVertexCount(t),this.getModel().getDrawableVertexIndices(t),this.getModel().getDrawableVertices(t),this.getModel().getDrawableVertexUvs(t),this.getModel().getMultiplyColor(t),this.getModel().getScreenColor(t),this.getModel().getDrawableOpacity(t),this.getModel().getDrawableBlendMode(t),this.getModel().getDrawableInvertedMaskBit(t))}}drawMesh(t,e,i,s,r,o,a,n,l,h,u){this.isCulling()?this.gl.enable(this.gl.CULL_FACE):this.gl.disable(this.gl.CULL_FACE),this.gl.frontFace(this.gl.CCW);const d=this.getModelColor();null==this.getClippingContextBufferForMask()&&(d.A*=l,this.isPremultipliedAlpha()&&(d.R*=d.A,d.G*=d.A,d.B*=d.A));let c=null;null!=this._textures[t]&&(c=this._textures[t]),Ht.getInstance().setupShaderProgram(this,c,i,r,s,o,this._bufferData,l,h,d,a,n,this.isPremultipliedAlpha(),this.getMvpMatrix(),u),this.gl.drawElements(this.gl.TRIANGLES,e,this.gl.UNSIGNED_SHORT,0),this.gl.useProgram(null),this.setClippingContextBufferForDraw(null),this.setClippingContextBufferForMask(null)}saveProfile(){this._rendererProfile.save()}restoreProfile(){this._rendererProfile.restore()}static doStaticRelease(){Ht.deleteInstance()}setRenderState(t,e){Nt=t,Vt=e}preDraw(){if(this.firstDraw&&(this.firstDraw=!1),this.gl.disable(this.gl.SCISSOR_TEST),this.gl.disable(this.gl.STENCIL_TEST),this.gl.disable(this.gl.DEPTH_TEST),this.gl.frontFace(this.gl.CW),this.gl.enable(this.gl.BLEND),this.gl.colorMask(!0,!0,!0,!0),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,null),this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,null),this.getAnisotropy()>0&&this._extension)for(const t of Object.entries(this._textures))this.gl.bindTexture(this.gl.TEXTURE_2D,t),this.gl.texParameterf(this.gl.TEXTURE_2D,this._extension.TEXTURE_MAX_ANISOTROPY_EXT,this.getAnisotropy())}setClippingContextBufferForMask(t){this._clippingContextBufferForMask=t}getClippingContextBufferForMask(){return this._clippingContextBufferForMask}setClippingContextBufferForDraw(t){this._clippingContextBufferForDraw=t}getClippingContextBufferForDraw(){return this._clippingContextBufferForDraw}startUp(t){this.gl=t,this._clippingManager&&this._clippingManager.setGL(t),Ht.getInstance().setGl(t),this._rendererProfile.setGl(t),this._extension=this.gl.getExtension("EXT_texture_filter_anisotropic")||this.gl.getExtension("WEBKIT_EXT_texture_filter_anisotropic")||this.gl.getExtension("MOZ_EXT_texture_filter_anisotropic")}}x.staticRelease=()=>{te.doStaticRelease()};class ee{constructor(t){this.groups=t.Groups,this.hitAreas=t.HitAreas,this.layout=t.Layout,this.moc=t.FileReferences.Moc,this.expressions=t.FileReferences.Expressions,this.motions=t.FileReferences.Motions,this.textures=t.FileReferences.Textures,this.physics=t.FileReferences.Physics,this.pose=t.FileReferences.Pose}getEyeBlinkParameters(){var t,e;return null==(e=null==(t=this.groups)?void 0:t.find((t=>"EyeBlink"===t.Name)))?void 0:e.Ids}getLipSyncParameters(){var t,e;return null==(e=null==(t=this.groups)?void 0:t.find((t=>"LipSync"===t.Name)))?void 0:e.Ids}}const ie="ParamAngleX",se="ParamAngleY",re="ParamAngleZ",oe="ParamEyeBallX",ae="ParamEyeBallY",ne="ParamMouthForm",le="ParamBodyAngleX",he="ParamBreath",ue={LOG_LEVEL_VERBOSE:0,LOG_LEVEL_WARNING:1,LOG_LEVEL_ERROR:2,LOG_LEVEL_NONE:999,logLevel:1,sound:!0,motionSync:!0,motionFadingDuration:500,idleMotionFadingDuration:2e3,expressionFadingDuration:500,preserveExpressionOnMotion:!0,cubism4:t.CubismConfig},de={log(t,...e){ue.logLevel<=ue.LOG_LEVEL_VERBOSE&&console.log(`[${t}]`,...e)},warn(t,...e){ue.logLevel<=ue.LOG_LEVEL_WARNING&&console.warn(`[${t}]`,...e)},error(t,...e){ue.logLevel<=ue.LOG_LEVEL_ERROR&&console.error(`[${t}]`,...e)}};function ce(t,e,i){return t<e?e:t>i?i:t}function ge(t,e){e.forEach((e=>{Object.getOwnPropertyNames(e.prototype).forEach((i=>{"constructor"!==i&&Object.defineProperty(t.prototype,i,Object.getOwnPropertyDescriptor(e.prototype,i))}))}))}function me(t){let e=t.lastIndexOf("/");return-1!=e&&(t=t.slice(0,e)),e=t.lastIndexOf("/"),-1!==e&&(t=t.slice(e+1)),t}function pe(t,e){const i=t.indexOf(e);-1!==i&&t.splice(i,1)}class _e extends e.utils.EventEmitter{constructor(t,e){super(),o(this,"tag"),o(this,"settings"),o(this,"expressions",[]),o(this,"defaultExpression"),o(this,"currentExpression"),o(this,"reserveExpressionIndex",-1),o(this,"destroyed",!1),this.settings=t,this.tag=`ExpressionManager(${t.name})`}init(){this.defaultExpression=this.createExpression({},void 0),this.currentExpression=this.defaultExpression,this.stopAllExpressions()}loadExpression(t){return a(this,null,(function*(){if(!this.definitions[t])return void de.warn(this.tag,`Undefined expression at [${t}]`);if(null===this.expressions[t])return void de.warn(this.tag,`Cannot set expression at [${t}] because it's already failed in loading.`);if(this.expressions[t])return this.expressions[t];const e=yield this._loadExpression(t);return this.expressions[t]=e,e}))}_loadExpression(t){throw new Error("Not implemented.")}setRandomExpression(){return a(this,null,(function*(){if(this.definitions.length){const t=[];for(let e=0;e<this.definitions.length;e++)null!==this.expressions[e]&&this.expressions[e]!==this.currentExpression&&e!==this.reserveExpressionIndex&&t.push(e);if(t.length){const e=Math.floor(Math.random()*t.length);return this.setExpression(e)}}return!1}))}resetExpression(){this._setExpression(this.defaultExpression)}restoreExpression(){this._setExpression(this.currentExpression)}setExpression(t){return a(this,null,(function*(){if("number"!=typeof t&&(t=this.getExpressionIndex(t)),!(t>-1&&t<this.definitions.length))return!1;if(t===this.expressions.indexOf(this.currentExpression))return!1;this.reserveExpressionIndex=t;const e=yield this.loadExpression(t);return!(!e||this.reserveExpressionIndex!==t)&&(this.reserveExpressionIndex=-1,this.currentExpression=e,this._setExpression(e),!0)}))}update(t,e){return!this.isFinished()&&this.updateParameters(t,e)}destroy(){this.destroyed=!0,this.emit("destroy");this.definitions=void 0,this.expressions=void 0}}const fe=40/7.5,xe=1/150;class Ce{constructor(){o(this,"targetX",0),o(this,"targetY",0),o(this,"x",0),o(this,"y",0),o(this,"vx",0),o(this,"vy",0)}focus(t,e,i=!1){this.targetX=ce(t,-1,1),this.targetY=ce(e,-1,1),i&&(this.x=this.targetX,this.y=this.targetY)}update(t){const e=this.targetX-this.x,i=this.targetY-this.y;if(Math.abs(e)<.01&&Math.abs(i)<.01)return;const s=Math.sqrt(r(e,2)+r(i,2)),o=fe/(1e3/t);let a=o*(e/s)-this.vx,n=o*(i/s)-this.vy;const l=Math.sqrt(r(a,2)+r(n,2)),h=o*xe*t;l>h&&(a*=h/l,n*=h/l),this.vx+=a,this.vy+=n;const u=Math.sqrt(r(this.vx,2)+r(this.vy,2)),d=.5*(Math.sqrt(r(h,2)+8*h*s)-h);u>d&&(this.vx*=d/u,this.vy*=d/u),this.x+=this.vx,this.y+=this.vy}}class ye{constructor(t){o(this,"json"),o(this,"name"),o(this,"url"),o(this,"pose"),o(this,"physics"),this.json=t;const e=t.url;if("string"!=typeof e)throw new TypeError("The `url` field in settings JSON must be defined as a string.");this.url=e,this.name=me(this.url)}resolveURL(t){return e.utils.url.resolve(this.url,t)}replaceFiles(t){this.moc=t(this.moc,"moc"),void 0!==this.pose&&(this.pose=t(this.pose,"pose")),void 0!==this.physics&&(this.physics=t(this.physics,"physics"));for(let e=0;e<this.textures.length;e++)this.textures[e]=t(this.textures[e],`textures[${e}]`)}getDefinedFiles(){const t=[];return this.replaceFiles((e=>(t.push(e),e))),t}validateFiles(t){const e=(e,i)=>{const s=this.resolveURL(e);if(!t.includes(s)){if(i)throw new Error(`File "${e}" is defined in settings, but doesn't exist in given files`);return!1}return!0};[this.moc,...this.textures].forEach((t=>e(t,!0)));return this.getDefinedFiles().filter((t=>e(t,!1)))}}var Me=(t=>(t[t.NONE=0]="NONE",t[t.IDLE=1]="IDLE",t[t.NORMAL=2]="NORMAL",t[t.FORCE=3]="FORCE",t))(Me||{});class ve{constructor(){o(this,"tag"),o(this,"debug",!1),o(this,"currentPriority",0),o(this,"reservePriority",0),o(this,"currentGroup"),o(this,"currentIndex"),o(this,"reservedGroup"),o(this,"reservedIndex"),o(this,"reservedIdleGroup"),o(this,"reservedIdleIndex")}reserve(t,e,i){if(i<=0)return de.log(this.tag,"Cannot start a motion with MotionPriority.NONE."),!1;if(t===this.currentGroup&&e===this.currentIndex)return de.log(this.tag,"Motion is already playing.",this.dump(t,e)),!1;if(t===this.reservedGroup&&e===this.reservedIndex||t===this.reservedIdleGroup&&e===this.reservedIdleIndex)return de.log(this.tag,"Motion is already reserved.",this.dump(t,e)),!1;if(1===i){if(0!==this.currentPriority)return de.log(this.tag,"Cannot start idle motion because another motion is playing.",this.dump(t,e)),!1;if(void 0!==this.reservedIdleGroup)return de.log(this.tag,"Cannot start idle motion because another idle motion has reserved.",this.dump(t,e)),!1;this.setReservedIdle(t,e)}else{if(i<3){if(i<=this.currentPriority)return de.log(this.tag,"Cannot start motion because another motion is playing as an equivalent or higher priority.",this.dump(t,e)),!1;if(i<=this.reservePriority)return de.log(this.tag,"Cannot start motion because another motion has reserved as an equivalent or higher priority.",this.dump(t,e)),!1}this.setReserved(t,e,i)}return!0}start(t,e,i,s){if(1===s){if(this.setReservedIdle(void 0,void 0),0!==this.currentPriority)return de.log(this.tag,"Cannot start idle motion because another motion is playing.",this.dump(e,i)),!1}else{if(e!==this.reservedGroup||i!==this.reservedIndex)return de.log(this.tag,"Cannot start motion because another motion has taken the place.",this.dump(e,i)),!1;this.setReserved(void 0,void 0,0)}return!!t&&(this.setCurrent(e,i,s),!0)}complete(){this.setCurrent(void 0,void 0,0)}setCurrent(t,e,i){this.currentPriority=i,this.currentGroup=t,this.currentIndex=e}setReserved(t,e,i){this.reservePriority=i,this.reservedGroup=t,this.reservedIndex=e}setReservedIdle(t,e){this.reservedIdleGroup=t,this.reservedIdleIndex=e}isActive(t,e){return t===this.currentGroup&&e===this.currentIndex||t===this.reservedGroup&&e===this.reservedIndex||t===this.reservedIdleGroup&&e===this.reservedIdleIndex}reset(){this.setCurrent(void 0,void 0,0),this.setReserved(void 0,void 0,0),this.setReservedIdle(void 0,void 0)}shouldRequestIdleMotion(){return void 0===this.currentGroup&&void 0===this.reservedIdleGroup}shouldOverrideExpression(){return!ue.preserveExpressionOnMotion&&this.currentPriority>1}dump(t,e){if(this.debug){return`\n<Requested> group = "${t}", index = ${e}\n`+["currentPriority","reservePriority","currentGroup","currentIndex","reservedGroup","reservedIndex","reservedIdleGroup","reservedIdleIndex"].map((t=>"["+t+"] "+this[t])).join("\n")}return""}}const Pe=.5;class be{static get volume(){return this._volume}static set volume(t){this._volume=(t>1?1:t<0?0:t)||0,this.audios.forEach((t=>t.volume=this._volume))}static add(t,e,i,s){const r=new Audio(t);return r.volume=this._volume,r.preload="auto",r.crossOrigin=s,r.addEventListener("ended",(()=>{this.dispose(r),null==e||e()})),r.addEventListener("error",(e=>{this.dispose(r),de.warn("SoundManager",`Error occurred on "${t}"`,e.error),null==i||i(e.error)})),this.audios.push(r),r}static play(t){return new Promise(((e,i)=>{var s;null==(s=t.play())||s.catch((e=>{t.dispatchEvent(new ErrorEvent("error",{error:e})),i(e)})),t.readyState===t.HAVE_ENOUGH_DATA?e():t.addEventListener("canplaythrough",e)}))}static addContext(t){const e=new AudioContext;return this.contexts.push(e),e}static addAnalyzer(t,e){const i=e.createMediaElementSource(t),s=e.createAnalyser();return s.fftSize=256,s.minDecibels=-90,s.maxDecibels=-10,s.smoothingTimeConstant=.85,i.connect(s),s.connect(e.destination),this.analysers.push(s),s}static analyze(t){if(null!=t){const e=new Float32Array(t.fftSize);let i=0;t.getFloatTimeDomainData(e);for(const t of e)i+=t*t;return parseFloat(Math.sqrt(i/e.length*20).toFixed(1))}return parseFloat(Math.random().toFixed(1))}static dispose(t){t.pause(),t.removeAttribute("src"),pe(this.audios,t)}static destroy(){for(let t=this.contexts.length-1;t>=0;t--)this.contexts[t].close();for(let t=this.audios.length-1;t>=0;t--)this.dispose(this.audios[t])}}o(be,"audios",[]),o(be,"analysers",[]),o(be,"contexts",[]),o(be,"_volume",Pe);var Se=(t=>(t.ALL="ALL",t.IDLE="IDLE",t.NONE="NONE",t))(Se||{});class we extends e.utils.EventEmitter{constructor(t,e){super(),o(this,"tag"),o(this,"settings"),o(this,"motionGroups",{}),o(this,"state",new ve),o(this,"currentAudio"),o(this,"currentAnalyzer"),o(this,"currentContext"),o(this,"playing",!1),o(this,"destroyed",!1),this.settings=t,this.tag=`MotionManager(${t.name})`,this.state.tag=this.tag}init(t){(null==t?void 0:t.idleMotionGroup)&&(this.groups.idle=t.idleMotionGroup),this.setupMotions(t),this.stopAllMotions()}setupMotions(t){for(const i of Object.keys(this.definitions))this.motionGroups[i]=[];let e;switch(null==t?void 0:t.motionPreload){case"NONE":return;case"ALL":e=Object.keys(this.definitions);break;default:e=[this.groups.idle]}for(const i of e)if(this.definitions[i])for(let t=0;t<this.definitions[i].length;t++)this.loadMotion(i,t).then()}loadMotion(t,e){return a(this,null,(function*(){var i;if(!(null==(i=this.definitions[t])?void 0:i[e]))return void de.warn(this.tag,`Undefined motion at "${t}"[${e}]`);if(null===this.motionGroups[t][e])return void de.warn(this.tag,`Cannot start motion at "${t}"[${e}] because it's already failed in loading.`);if(this.motionGroups[t][e])return this.motionGroups[t][e];const s=yield this._loadMotion(t,e);return this.destroyed?void 0:(this.motionGroups[t][e]=null!=s?s:null,s)}))}_loadMotion(t,e){throw new Error("Not implemented.")}speak(t){return a(this,arguments,(function*(t,{volume:e=.5,expression:i,resetExpression:s=!0,crossOrigin:r,onFinish:o,onError:a}={}){if(!ue.sound)return!1;let n,l,h,u;if(this.currentAudio&&!this.currentAudio.ended)return!1;const d=t&&t.startsWith("data:");if(console.log(o),t&&!d){const e=document.createElement("a");e.href=t,u=t=e.href}else u="data:audio/";const c=t;if(c)try{n=be.add(c,((t=this)=>{console.log("Audio finished playing"),null==o||o(),s&&i&&t.expressionManager&&t.expressionManager.resetExpression(),t.currentAudio=void 0}),((t,e=this)=>{console.log("Error during audio playback:",t),null==a||a(t),s&&i&&e.expressionManager&&e.expressionManager.resetExpression(),e.currentAudio=void 0}),r),this.currentAudio=n,be.volume=e,h=be.addContext(this.currentAudio),this.currentContext=h,l=be.addAnalyzer(this.currentAudio,this.currentContext),this.currentAnalyzer=l}catch(g){return de.warn(this.tag,"Failed to create audio",u,g),!1}if(n){let t=!0;const e=be.play(n).catch((e=>{de.warn(this.tag,"Failed to play audio",n.src,e),t=!1}));if(ue.motionSync&&(yield e,!t))return!1}return this.state.shouldOverrideExpression()&&this.expressionManager&&this.expressionManager.resetExpression(),i&&this.expressionManager&&this.expressionManager.setExpression(i),this.playing=!0,!0}))}startMotion(t,e){return a(this,arguments,(function*(t,e,i=Me.NORMAL,{sound:s,volume:r=.5,expression:o,resetExpression:a=!0,crossOrigin:n,onFinish:l,onError:h}={}){var u;if(!this.state.reserve(t,e,i))return!1;if(this.currentAudio&&!this.currentAudio.ended&&i!=Me.FORCE)return!1;const d=null==(u=this.definitions[t])?void 0:u[e];if(!d)return!1;let c,g,m,p;this.currentAudio&&be.dispose(this.currentAudio);const _=s&&s.startsWith("data:");if(s&&!_){const t=document.createElement("a");t.href=s,p=s=t.href}else p=this.getSoundFile(d),p&&(p=this.settings.resolveURL(p));const f=p;if(f)try{c=be.add(f,((t=this)=>{console.log("Audio finished playing"),null==l||l(),console.log(l),a&&o&&t.expressionManager&&t.expressionManager.resetExpression(),t.currentAudio=void 0}),((t,e=this)=>{console.log("Error during audio playback:",t),null==h||h(t),a&&o&&e.expressionManager&&e.expressionManager.resetExpression(),e.currentAudio=void 0}),n),this.currentAudio=c,be.volume=r,m=be.addContext(this.currentAudio),this.currentContext=m,g=be.addAnalyzer(this.currentAudio,this.currentContext),this.currentAnalyzer=g}catch(C){de.warn(this.tag,"Failed to create audio",p,C)}const x=yield this.loadMotion(t,e);if(c){const t=be.play(c).catch((t=>de.warn(this.tag,"Failed to play audio",c.src,t)));ue.motionSync&&(yield t)}return this.state.start(x,t,e,i)?(this.state.shouldOverrideExpression()&&this.expressionManager&&this.expressionManager.resetExpression(),de.log(this.tag,"Start motion:",this.getMotionName(d)),this.emit("motionStart",t,e,c),o&&this.expressionManager&&this.state.shouldOverrideExpression()&&this.expressionManager.setExpression(o),this.playing=!0,this._startMotion(x),!0):(c&&(be.dispose(c),this.currentAudio=void 0),!1)}))}startRandomMotion(t,e){return a(this,arguments,(function*(t,e,{sound:i,volume:s=.5,expression:r,resetExpression:o=!0,crossOrigin:a,onFinish:n,onError:l}={}){const h=this.definitions[t];if(null==h?void 0:h.length){const u=[];for(let e=0;e<h.length;e++)null===this.motionGroups[t][e]||this.state.isActive(t,e)||u.push(e);if(u.length){const h=u[Math.floor(Math.random()*u.length)];return this.startMotion(t,h,e,{sound:i,volume:s,expression:r,resetExpression:o,crossOrigin:a,onFinish:n,onError:l})}}return!1}))}stopSpeaking(){this.currentAudio&&(be.dispose(this.currentAudio),this.currentAudio=void 0)}stopAllMotions(){this._stopAllMotions(),this.state.reset(),this.stopSpeaking()}update(t,e){var i;return this.isFinished()&&(this.playing&&(this.playing=!1,this.emit("motionFinish")),this.state.shouldOverrideExpression()&&(null==(i=this.expressionManager)||i.restoreExpression()),this.state.complete(),this.state.shouldRequestIdleMotion()&&this.startRandomMotion(this.groups.idle,Me.IDLE)),this.updateParameters(t,e)}mouthSync(){return this.currentAnalyzer?be.analyze(this.currentAnalyzer):0}destroy(){var t;this.destroyed=!0,this.emit("destroy"),this.stopAllMotions(),null==(t=this.expressionManager)||t.destroy();this.definitions=void 0,this.motionGroups=void 0}}const Te={x:0,y:0,width:0,height:0};class Ie extends e.utils.EventEmitter{constructor(){super(...arguments),o(this,"focusController",new Ce),o(this,"pose"),o(this,"physics"),o(this,"originalWidth",0),o(this,"originalHeight",0),o(this,"width",0),o(this,"height",0),o(this,"localTransform",new e.Matrix),o(this,"drawingMatrix",new e.Matrix),o(this,"hitAreas",{}),o(this,"textureFlipY",!1),o(this,"viewport",[0,0,0,0]),o(this,"destroyed",!1)}init(){this.setupLayout(),this.setupHitAreas()}setupLayout(){const t=this,e=this.getSize();t.originalWidth=e[0],t.originalHeight=e[1];const i=Object.assign({width:2,height:2},this.getLayout());this.localTransform.scale(i.width/2,i.height/2),t.width=this.originalWidth*this.localTransform.a,t.height=this.originalHeight*this.localTransform.d;const s=void 0!==i.x&&i.x-i.width/2||void 0!==i.centerX&&i.centerX||void 0!==i.left&&i.left-i.width/2||void 0!==i.right&&i.right+i.width/2||0,r=void 0!==i.y&&i.y-i.height/2||void 0!==i.centerY&&i.centerY||void 0!==i.top&&i.top-i.height/2||void 0!==i.bottom&&i.bottom+i.height/2||0;this.localTransform.translate(this.width*s,-this.height*r)}setupHitAreas(){const t=this.getHitAreaDefs().filter((t=>t.index>=0));for(const e of t)this.hitAreas[e.name]=e}hitTest(t,e){return Object.keys(this.hitAreas).filter((i=>this.isHit(i,t,e)))}isHit(t,e,i){if(!this.hitAreas[t])return!1;const s=this.hitAreas[t].index,r=this.getDrawableBounds(s,Te);return r.x<=e&&e<=r.x+r.width&&r.y<=i&&i<=r.y+r.height}getDrawableBounds(t,e){const i=this.getDrawableVertices(t);let s=i[0],r=i[0],o=i[1],a=i[1];for(let n=0;n<i.length;n+=2){const t=i[n],e=i[n+1];s=Math.min(t,s),r=Math.max(t,r),o=Math.min(e,o),a=Math.max(e,a)}return null!=e||(e={}),e.x=s,e.y=o,e.width=r-s,e.height=a-o,e}updateTransform(t){this.drawingMatrix.copyFrom(t).append(this.localTransform)}update(t,e){this.focusController.update(t)}destroy(){this.destroyed=!0,this.emit("destroy"),this.motionManager.destroy(),this.motionManager=void 0}}class Ee extends Error{constructor(t,e,i,s=!1){super(t),this.url=e,this.status=i,this.aborted=s}}const Le=class t{static createXHR(e,i,s,r,o){const a=new XMLHttpRequest;if(t.allXhrSet.add(a),e){let i=t.xhrMap.get(e);i?i.add(a):(i=new Set([a]),t.xhrMap.set(e,i)),e.listeners("destroy").includes(t.cancelXHRs)||e.once("destroy",t.cancelXHRs)}return a.open("GET",i),a.responseType=s,a.onload=()=>{200!==a.status&&0!==a.status||!a.response?a.onerror():r(a.response)},a.onerror=()=>{de.warn("XHRLoader",`Failed to load resource as ${a.responseType} (Status ${a.status}): ${i}`),o(new Ee("Network error.",i,a.status))},a.onabort=()=>o(new Ee("Aborted.",i,a.status,!0)),a.onloadend=()=>{var i;t.allXhrSet.delete(a),e&&(null==(i=t.xhrMap.get(e))||i.delete(a))},a}static cancelXHRs(){var e;null==(e=t.xhrMap.get(this))||e.forEach((e=>{e.abort(),t.allXhrSet.delete(e)})),t.xhrMap.delete(this)}static release(){t.allXhrSet.forEach((t=>t.abort())),t.allXhrSet.clear(),t.xhrMap=new WeakMap}};o(Le,"xhrMap",new WeakMap),o(Le,"allXhrSet",new Set),o(Le,"loader",((t,e)=>new Promise(((e,i)=>{Le.createXHR(t.target,t.settings?t.settings.resolveURL(t.url):t.url,t.type,(i=>{t.result=i,e()}),i).send()}))));let Fe=Le;function Ae(t,e){let i=-1;return function s(r,o){if(o)return Promise.reject(o);if(r<=i)return Promise.reject(new Error("next() called multiple times"));i=r;const a=t[r];if(!a)return Promise.resolve();try{return Promise.resolve(a(e,s.bind(null,r+1)))}catch(n){return Promise.reject(n)}}(0)}class De{static load(t){return Ae(this.middlewares,t).then((()=>t.result))}}function Be(){}o(De,"middlewares",[Fe.loader]);const Re="Live2DFactory",Oe=(t,e)=>a(this,null,(function*(){if("string"==typeof t.source){const e=yield De.load({url:t.source,type:"json",target:t.live2dModel});e.url=t.source,t.source=e,t.live2dModel.emit("settingsJSONLoaded",e)}return e()})),ke=(t,e)=>a(this,null,(function*(){if(t.source instanceof ye)return t.settings=t.source,e();if("object"==typeof t.source){const i=Ye.findRuntime(t.source);if(i){const s=i.createModelSettings(t.source);return t.settings=s,t.live2dModel.emit("settingsLoaded",s),e()}}throw new TypeError("Unknown settings format.")})),Ue=(t,e)=>{if(t.settings){const i=Ye.findRuntime(t.settings);if(i)return i.ready().then(e)}return e()},Ve=(t,e)=>a(this,null,(function*(){yield e();const i=t.internalModel;if(i){const e=t.settings,s=Ye.findRuntime(e);if(s){const r=[];e.pose&&r.push(De.load({settings:e,url:e.pose,type:"json",target:i}).then((e=>{i.pose=s.createPose(i.coreModel,e),t.live2dModel.emit("poseLoaded",i.pose)})).catch((e=>{t.live2dModel.emit("poseLoadError",e),de.warn(Re,"Failed to load pose.",e)}))),e.physics&&r.push(De.load({settings:e,url:e.physics,type:"json",target:i}).then((e=>{i.physics=s.createPhysics(i.coreModel,e),t.live2dModel.emit("physicsLoaded",i.physics)})).catch((e=>{t.live2dModel.emit("physicsLoadError",e),de.warn(Re,"Failed to load physics.",e)}))),r.length&&(yield Promise.all(r))}}})),Ne=(t,i)=>a(this,null,(function*(){if(!t.settings)throw new TypeError("Missing settings.");{const s=t.live2dModel,r=Promise.all(t.settings.textures.map((i=>function(t,i={}){const s={resourceOptions:{crossorigin:i.crossOrigin}};if(e.Texture.fromURL)return e.Texture.fromURL(t,s).catch((t=>{if(t instanceof Error)throw t;const e=new Error("Texture loading error");throw e.event=t,e}));s.resourceOptions.autoLoad=!1;const r=e.Texture.from(t,s);if(r.baseTexture.valid)return Promise.resolve(r);const o=r.baseTexture.resource;return null!=o._live2d_load||(o._live2d_load=new Promise(((t,e)=>{const i=t=>{o.source.removeEventListener("error",i);const s=new Error("Texture loading error");s.event=t,e(s)};o.source.addEventListener("error",i),o.load().then((()=>t(r))).catch(i)}))),o._live2d_load}(t.settings.resolveURL(i),{crossOrigin:t.options.crossOrigin}))));if(r.catch(Be),yield i(),!t.internalModel)throw new TypeError("Missing internal model.");s.internalModel=t.internalModel,s.emit("modelLoaded",t.internalModel),s.textures=yield r,s.emit("textureLoaded",s.textures)}})),Ge=(t,e)=>a(this,null,(function*(){const i=t.settings;if(i instanceof ye){const s=Ye.findRuntime(i);if(!s)throw new TypeError("Unknown model settings.");const r=yield De.load({settings:i,url:i.moc,type:"arraybuffer",target:t.live2dModel});if(!s.isValidMoc(r))throw new Error("Invalid moc data");const o=s.createCoreModel(r);return t.internalModel=s.createInternalModel(o,i,t.options),e()}throw new TypeError("Missing settings.")})),Xe=class t{static unzip(i,s){return a(this,null,(function*(){const r=yield t.getFilePaths(i),o=[];for(const t of s.getDefinedFiles()){const i=decodeURI(e.utils.url.resolve(s.url,t));r.includes(i)&&o.push(i)}const a=yield t.getFiles(i,o);for(let t=0;t<a.length;t++){const e=o[t],i=a[t];Object.defineProperty(i,"webkitRelativePath",{value:e})}return a}))}static createSettings(e){return a(this,null,(function*(){const i=(yield t.getFilePaths(e)).find((t=>t.endsWith("model.json")||t.endsWith("model3.json")));if(!i)throw new Error("Settings file not found");const s=yield t.readText(e,i);if(!s)throw new Error("Empty settings file: "+i);const r=JSON.parse(s);r.url=i;const o=t.live2dFactory.findRuntime(r);if(!o)throw new Error("Unknown settings JSON");return o.createModelSettings(r)}))}static zipReader(t,e){return a(this,null,(function*(){throw new Error("Not implemented")}))}static getFilePaths(t){return a(this,null,(function*(){throw new Error("Not implemented")}))}static getFiles(t,e){return a(this,null,(function*(){throw new Error("Not implemented")}))}static readText(t,e){return a(this,null,(function*(){throw new Error("Not implemented")}))}static releaseReader(t){}};o(Xe,"live2dFactory"),o(Xe,"ZIP_PROTOCOL","zip://"),o(Xe,"uid",0),o(Xe,"factory",((t,e)=>a(Xe,null,(function*(){const i=t.source;let s,r,o;if("string"==typeof i&&(i.endsWith(".zip")||i.startsWith(Xe.ZIP_PROTOCOL))?(s=i.startsWith(Xe.ZIP_PROTOCOL)?i.slice(Xe.ZIP_PROTOCOL.length):i,r=yield De.load({url:s,type:"blob",target:t.live2dModel})):Array.isArray(i)&&1===i.length&&i[0]instanceof File&&i[0].name.endsWith(".zip")&&(r=i[0],s=URL.createObjectURL(r),o=i.settings),r){if(!r.size)throw new Error("Empty zip file");const e=yield Xe.zipReader(r,s);o||(o=yield Xe.createSettings(e)),o._objectURL=Xe.ZIP_PROTOCOL+Xe.uid+"/"+o.url;const i=yield Xe.unzip(e,o);i.settings=o,t.source=i,s.startsWith("blob:")&&t.live2dModel.once("modelLoaded",(t=>{t.once("destroy",(function(){URL.revokeObjectURL(s)}))})),Xe.releaseReader(e)}return e()}))));let ze=Xe;const je=class t{static resolveURL(e,i){var s;const r=null==(s=t.filesMap[e])?void 0:s[i];if(void 0===r)throw new Error("Cannot find this file from uploaded files: "+i);return r}static upload(i,s){return a(this,null,(function*(){const r={};for(const t of s.getDefinedFiles()){const o=decodeURI(e.utils.url.resolve(s.url,t)),a=i.find((t=>t.webkitRelativePath===o));a&&(r[t]=URL.createObjectURL(a))}t.filesMap[s._objectURL]=r}))}static createSettings(e){return a(this,null,(function*(){const i=e.find((t=>t.name.endsWith("model.json")||t.name.endsWith("model3.json")));if(!i)throw new TypeError("Settings file not found");const s=yield t.readText(i),r=JSON.parse(s);r.url=i.webkitRelativePath;const o=Ye.findRuntime(r);if(!o)throw new Error("Unknown settings JSON");const a=o.createModelSettings(r);return a._objectURL=URL.createObjectURL(i),a}))}static readText(t){return a(this,null,(function*(){return new Promise(((e,i)=>{const s=new FileReader;s.onload=()=>e(s.result),s.onerror=i,s.readAsText(t,"utf8")}))}))}};o(je,"live2dFactory"),o(je,"filesMap",{}),o(je,"factory",((t,e)=>a(je,null,(function*(){if(Array.isArray(t.source)&&t.source[0]instanceof File){const e=t.source;let i=e.settings;if(i){if(!i._objectURL)throw new Error('"_objectURL" must be specified in ModelSettings')}else i=yield je.createSettings(e);i.validateFiles(e.map((t=>encodeURI(t.webkitRelativePath)))),yield je.upload(e,i),i.resolveURL=function(t){return je.resolveURL(this._objectURL,t)},t.source=i,t.live2dModel.once("modelLoaded",(t=>{t.once("destroy",(function(){const t=this.settings._objectURL;if(URL.revokeObjectURL(t),je.filesMap[t])for(const e of Object.values(je.filesMap[t]))URL.revokeObjectURL(e);delete je.filesMap[t]}))}))}return e()}))));let He=je;const We=class t{static registerRuntime(e){t.runtimes.push(e),t.runtimes.sort(((t,e)=>e.version-t.version))}static findRuntime(e){for(const i of t.runtimes)if(i.test(e))return i}static setupLive2DModel(e,i,s){return a(this,null,(function*(){const r=new Promise((t=>e.once("textureLoaded",t))),o=new Promise((t=>e.once("modelLoaded",t))),a=Promise.all([r,o]).then((()=>e.emit("ready")));yield Ae(t.live2DModelMiddlewares,{live2dModel:e,source:i,options:s||{}}),yield a,e.emit("load")}))}static loadMotion(e,i,s){var r;const o=t=>e.emit("motionLoadError",i,s,t);try{const a=null==(r=e.definitions[i])?void 0:r[s];if(!a)return Promise.resolve(void 0);e.listeners("destroy").includes(t.releaseTasks)||e.once("destroy",t.releaseTasks);let n=t.motionTasksMap.get(e);n||(n={},t.motionTasksMap.set(e,n));let l=n[i];l||(l=[],n[i]=l);const h=e.getMotionFile(a);return null!=l[s]||(l[s]=De.load({url:h,settings:e.settings,type:e.motionDataType,target:e}).then((r=>{var o;const n=null==(o=t.motionTasksMap.get(e))?void 0:o[i];n&&delete n[s];const l=e.createMotion(r,i,a);return e.emit("motionLoaded",i,s,l),l})).catch((t=>{de.warn(e.tag,`Failed to load motion: ${h}\n`,t),o(t)}))),l[s]}catch(a){de.warn(e.tag,`Failed to load motion at "${i}"[${s}]\n`,a),o(a)}return Promise.resolve(void 0)}static loadExpression(e,i){const s=t=>e.emit("expressionLoadError",i,t);try{const r=e.definitions[i];if(!r)return Promise.resolve(void 0);e.listeners("destroy").includes(t.releaseTasks)||e.once("destroy",t.releaseTasks);let o=t.expressionTasksMap.get(e);o||(o=[],t.expressionTasksMap.set(e,o));const a=e.getExpressionFile(r);return null!=o[i]||(o[i]=De.load({url:a,settings:e.settings,type:"json",target:e}).then((s=>{const o=t.expressionTasksMap.get(e);o&&delete o[i];const a=e.createExpression(s,r);return e.emit("expressionLoaded",i,a),a})).catch((t=>{de.warn(e.tag,`Failed to load expression: ${a}\n`,t),s(t)}))),o[i]}catch(r){de.warn(e.tag,`Failed to load expression at [${i}]\n`,r),s(r)}return Promise.resolve(void 0)}static releaseTasks(){this instanceof we?t.motionTasksMap.delete(this):t.expressionTasksMap.delete(this)}};o(We,"runtimes",[]),o(We,"urlToJSON",Oe),o(We,"jsonToSettings",ke),o(We,"waitUntilReady",Ue),o(We,"setupOptionals",Ve),o(We,"setupEssentials",Ne),o(We,"createInternalModel",Ge),o(We,"live2DModelMiddlewares",[ze.factory,He.factory,Oe,ke,Ue,Ve,Ne,Ge]),o(We,"motionTasksMap",new WeakMap),o(We,"expressionTasksMap",new WeakMap);let Ye=We;we.prototype._loadMotion=function(t,e){return Ye.loadMotion(this,t,e)},_e.prototype._loadExpression=function(t){return Ye.loadExpression(this,t)},He.live2dFactory=Ye,ze.live2dFactory=Ye;const qe=class t{constructor(e,{autoUpdate:i=!0,autoHitTest:s=!0,autoFocus:r=!0,autoInteract:a,ticker:n}={}){o(this,"model"),o(this,"destroyed",!1),o(this,"_ticker"),o(this,"_autoUpdate",!1),o(this,"_autoHitTest",!1),o(this,"_autoFocus",!1),n||(t.defaultTicker?n=t.defaultTicker:"undefined"!=typeof PIXI&&(n=PIXI.Ticker.shared)),void 0!==a&&(s=a,r=a,de.warn(e.tag,"options.autoInteract is deprecated since v0.5.0, use autoHitTest and autoFocus instead.")),this.model=e,this.ticker=n,this.autoUpdate=i,this.autoHitTest=s,this.autoFocus=r,(s||r)&&(this.model.eventMode="static")}get ticker(){return this._ticker}set ticker(t){var e;this._ticker&&this._ticker.remove(Je,this),this._ticker=t,this._autoUpdate&&(null==(e=this._ticker)||e.add(Je,this))}get autoUpdate(){return this._autoUpdate}set autoUpdate(t){var e;this.destroyed||(t?this._ticker?(this._ticker.add(Je,this),this._autoUpdate=!0):de.warn(this.model.tag,"No Ticker to be used for automatic updates. Either set option.ticker when creating Live2DModel, or expose PIXI to global scope (window.PIXI = PIXI)."):(null==(e=this._ticker)||e.remove(Je,this),this._autoUpdate=!1))}get autoHitTest(){return this._autoHitTest}set autoHitTest(t){t!==this.autoHitTest&&(t?this.model.on("pointertap",Ze,this):this.model.off("pointertap",Ze,this),this._autoHitTest=t)}get autoFocus(){return this._autoFocus}set autoFocus(t){t!==this.autoFocus&&(t?this.model.on("globalpointermove",Qe,this):this.model.off("globalpointermove",Qe,this),this._autoFocus=t)}get autoInteract(){return this._autoHitTest&&this._autoFocus}set autoInteract(t){this.autoHitTest=t,this.autoFocus=t}onTickerUpdate(){const t=this.ticker.deltaMS;this.model.update(t)}onTap(t){this.model.tap(t.global.x,t.global.y)}onPointerMove(t){this.model.focus(t.global.x,t.global.y)}destroy(){this.autoFocus=!1,this.autoHitTest=!1,this.autoUpdate=!1,this.ticker=void 0,this.destroyed=!0}};o(qe,"defaultTicker");let $e=qe;function Je(){this.onTickerUpdate()}function Ze(t){this.onTap(t)}function Qe(t){this.onPointerMove(t)}class Ke extends e.Transform{}const ti=new e.Point,ei=new e.Matrix;class ii extends i.Container{constructor(t){super(),o(this,"tag","Live2DModel(uninitialized)"),o(this,"internalModel"),o(this,"textures",[]),o(this,"transform",new Ke),o(this,"anchor",new e.ObservablePoint(this.onAnchorChange,this,0,0)),o(this,"glContextID",-1),o(this,"elapsedTime",0),o(this,"deltaTime",0),o(this,"automator"),this.automator=new $e(this,t),this.once("modelLoaded",(()=>this.init(t)))}static from(t,e){const i=new this(e);return Ye.setupLive2DModel(i,t,e).then((()=>i))}static fromSync(t,e){const i=new this(e);return Ye.setupLive2DModel(i,t,e).then(null==e?void 0:e.onLoad).catch(null==e?void 0:e.onError),i}static registerTicker(t){$e.defaultTicker=t.shared}init(t){this.tag=`Live2DModel(${this.internalModel.settings.name})`}onAnchorChange(){this.pivot.set(this.anchor.x*this.internalModel.width,this.anchor.y*this.internalModel.height)}motion(t,e,i,{sound:s,volume:r=.5,expression:o,resetExpression:a=!0,crossOrigin:n,onFinish:l,onError:h}={}){return void 0===e?this.internalModel.motionManager.startRandomMotion(t,i,{sound:s,volume:r,expression:o,resetExpression:a,crossOrigin:n,onFinish:l,onError:h}):this.internalModel.motionManager.startMotion(t,e,i,{sound:s,volume:r,expression:o,resetExpression:a,crossOrigin:n,onFinish:l,onError:h})}stopMotions(){return this.internalModel.motionManager.stopAllMotions()}speak(t,{volume:e=.5,expression:i,resetExpression:s=!0,crossOrigin:r,onFinish:o,onError:a}={}){return this.internalModel.motionManager.speak(t,{volume:e,expression:i,resetExpression:s,crossOrigin:r,onFinish:o,onError:a})}stopSpeaking(){return this.internalModel.motionManager.stopSpeaking()}expression(t){return this.internalModel.motionManager.expressionManager?void 0===t?this.internalModel.motionManager.expressionManager.setRandomExpression():this.internalModel.motionManager.expressionManager.setExpression(t):Promise.resolve(!1)}focus(t,e,i=!1){ti.x=t,ti.y=e,this.toModelPosition(ti,ti,!0);const s=ti.x/this.internalModel.originalWidth*2-1,r=ti.y/this.internalModel.originalHeight*2-1,o=Math.atan2(r,s);this.internalModel.focusController.focus(Math.cos(o),-Math.sin(o),i)}tap(t,e){const i=this.hitTest(t,e);i.length&&(de.log(this.tag,"Hit",i),this.emit("hit",i))}hitTest(t,e){return ti.x=t,ti.y=e,this.toModelPosition(ti,ti),this.internalModel.hitTest(ti.x,ti.y)}toModelPosition(t,e=t.clone(),i){return i||(this._recursivePostUpdateTransform(),this.parent?this.displayObjectUpdateTransform():(this.parent=this._tempDisplayObjectParent,this.displayObjectUpdateTransform(),this.parent=null)),this.transform.worldTransform.applyInverse(t,e),this.internalModel.localTransform.applyInverse(e,e),e}containsPoint(t){return this.getBounds(!0).contains(t.x,t.y)}_calculateBounds(){this._bounds.addFrame(this.transform,0,0,this.internalModel.width,this.internalModel.height)}update(t){this.deltaTime+=t,this.elapsedTime+=t}_render(t){t.batch.reset(),t.geometry.reset(),t.shader.reset(),t.state.reset();let e=!1;this.glContextID!==t.CONTEXT_UID&&(this.glContextID=t.CONTEXT_UID,this.internalModel.updateWebGLContext(t.gl,this.glContextID),e=!0);for(let r=0;r<this.textures.length;r++){const i=this.textures[r];i.valid&&(!e&&i.baseTexture._glTextures[this.glContextID]||(t.gl.pixelStorei(WebGLRenderingContext.UNPACK_FLIP_Y_WEBGL,this.internalModel.textureFlipY),t.texture.bind(i.baseTexture,0)),this.internalModel.bindTexture(r,i.baseTexture._glTextures[this.glContextID].texture),i.baseTexture.touched=t.textureGC.count)}const i=t.framebuffer.viewport;this.internalModel.viewport=[i.x,i.y,i.width,i.height],this.deltaTime&&(this.internalModel.update(this.deltaTime,this.elapsedTime),this.deltaTime=0);const s=ei.copyFrom(t.globalUniforms.uniforms.projectionMatrix).append(this.worldTransform);this.internalModel.updateTransform(s),this.internalModel.draw(t.gl),t.state.reset(),t.texture.reset()}destroy(t){this.emit("destroy"),(null==t?void 0:t.texture)&&this.textures.forEach((e=>e.destroy(t.baseTexture))),this.automator.destroy(),this.internalModel.destroy(),super.destroy(t)}}if(!window.Live2DCubismCore)throw new Error("Could not find Cubism 4 runtime. This plugin requires live2dcubismcore.js to be loaded.");class si extends _e{constructor(t,e){var i;super(t,e),o(this,"queueManager",new ht),o(this,"definitions"),this.definitions=null!=(i=t.expressions)?i:[],this.init()}isFinished(){return this.queueManager.isFinished()}getExpressionIndex(t){return this.definitions.findIndex((e=>e.Name===t))}getExpressionFile(t){return t.File}createExpression(t,e){return G.create(t)}_setExpression(t){return this.queueManager.startMotion(t,!1,performance.now())}stopAllExpressions(){this.queueManager.stopAllMotions()}updateParameters(t,e){return this.queueManager.doUpdateMotion(t,e)}}class ri extends we{constructor(t,e){var i;super(t,e),o(this,"definitions"),o(this,"groups",{idle:"Idle"}),o(this,"motionDataType","json"),o(this,"queueManager",new ht),o(this,"expressionManager"),o(this,"eyeBlinkIds"),o(this,"lipSyncIds",["ParamMouthOpenY"]),this.definitions=null!=(i=t.motions)?i:{},this.eyeBlinkIds=t.getEyeBlinkParameters()||[];const s=t.getLipSyncParameters();(null==s?void 0:s.length)&&(this.lipSyncIds=s),this.init(e)}init(t){super.init(t),this.settings.expressions&&(this.expressionManager=new si(this.settings,t)),this.queueManager.setEventCallback(((t,e,i)=>{this.emit("motion:"+e)}))}isFinished(){return this.queueManager.isFinished()}_startMotion(t,e){return t.setFinishedMotionHandler(e),this.queueManager.stopAllMotions(),this.queueManager.startMotion(t,!1,performance.now())}_stopAllMotions(){this.queueManager.stopAllMotions()}createMotion(t,e,i){const s=nt.create(t),r=new Z(t),o=(e===this.groups.idle?ue.idleMotionFadingDuration:ue.motionFadingDuration)/1e3;return void 0===r.getMotionFadeInTime()&&s.setFadeInTime(i.FadeInTime>0?i.FadeInTime:o),void 0===r.getMotionFadeOutTime()&&s.setFadeOutTime(i.FadeOutTime>0?i.FadeOutTime:o),s.setEffectIds(this.eyeBlinkIds,this.lipSyncIds),s}getMotionFile(t){return t.File}getMotionName(t){return t.File}getSoundFile(t){return t.Sound}updateParameters(t,e){return this.queueManager.doUpdateMotion(t,e)}destroy(){super.destroy(),this.queueManager.release(),this.queueManager=void 0}}const oi=new f;class ai extends Ie{constructor(t,i,s){super(),o(this,"settings"),o(this,"coreModel"),o(this,"motionManager"),o(this,"lipSync",!0),o(this,"breath",n.create()),o(this,"eyeBlink"),o(this,"userData"),o(this,"renderer",new te),o(this,"idParamAngleX",ie),o(this,"idParamAngleY",se),o(this,"idParamAngleZ",re),o(this,"idParamEyeBallX",oe),o(this,"idParamEyeBallY",ae),o(this,"idParamBodyAngleX",le),o(this,"idParamBreath",he),o(this,"idParamMouthForm",ne),o(this,"pixelsPerUnit",1),o(this,"centeringTransform",new e.Matrix),this.coreModel=t,this.settings=i,this.motionManager=new ri(i,s),this.init()}init(){var t;super.init(),(null==(t=this.settings.getEyeBlinkParameters())?void 0:t.length)&&(this.eyeBlink=u.create(this.settings)),this.breath.setParameters([new l(this.idParamAngleX,0,15,6.5345,.5),new l(this.idParamAngleY,0,8,3.5345,.5),new l(this.idParamAngleZ,0,10,5.5345,.5),new l(this.idParamBodyAngleX,0,4,15.5345,.5),new l(this.idParamBreath,0,.5,3.2345,.5)]),this.renderer.initialize(this.coreModel),this.renderer.setIsPremultipliedAlpha(!0)}getSize(){return[this.coreModel.getModel().canvasinfo.CanvasWidth,this.coreModel.getModel().canvasinfo.CanvasHeight]}getLayout(){const t={};if(this.settings.layout)for(const[e,i]of Object.entries(this.settings.layout)){t[e.charAt(0).toLowerCase()+e.slice(1)]=i}return t}setupLayout(){super.setupLayout(),this.pixelsPerUnit=this.coreModel.getModel().canvasinfo.PixelsPerUnit,this.centeringTransform.scale(this.pixelsPerUnit,this.pixelsPerUnit).translate(this.originalWidth/2,this.originalHeight/2)}updateWebGLContext(t,e){this.renderer.firstDraw=!0,this.renderer._bufferData={vertex:null,uv:null,index:null},this.renderer.startUp(t),this.renderer._clippingManager._currentFrameNo=e,this.renderer._clippingManager._maskTexture=void 0,Ht.getInstance()._shaderSets=[]}bindTexture(t,e){this.renderer.bindTexture(t,e)}getHitAreaDefs(){var t,e;return null!=(e=null==(t=this.settings.hitAreas)?void 0:t.map((t=>({id:t.Id,name:t.Name,index:this.coreModel.getDrawableIndex(t.Id)}))))?e:[]}getDrawableIDs(){return this.coreModel.getDrawableIds()}getDrawableIndex(t){return this.coreModel.getDrawableIndex(t)}getDrawableVertices(t){if("string"==typeof t&&-1===(t=this.coreModel.getDrawableIndex(t)))throw new TypeError("Unable to find drawable ID: "+t);const e=this.coreModel.getDrawableVertices(t).slice();for(let i=0;i<e.length;i+=2)e[i]=e[i]*this.pixelsPerUnit+this.originalWidth/2,e[i+1]=-e[i+1]*this.pixelsPerUnit+this.originalHeight/2;return e}updateTransform(t){this.drawingMatrix.copyFrom(this.centeringTransform).prepend(this.localTransform).prepend(t)}update(t,e){var i,s,r,o;super.update(t,e),t/=1e3,e/=1e3;const a=this.coreModel;this.emit("beforeMotionUpdate");const n=this.motionManager.update(this.coreModel,e);if(this.emit("afterMotionUpdate"),a.saveParameters(),null==(i=this.motionManager.expressionManager)||i.update(a,e),n||null==(s=this.eyeBlink)||s.updateParameters(a,t),this.updateFocus(),this.updateNaturalMovements(1e3*t,1e3*e),this.lipSync&&this.motionManager.currentAudio){let t=this.motionManager.mouthSync(),e=0;t>0&&(e=.4),t=ce(t*1.2,e,1);for(let i=0;i<this.motionManager.lipSyncIds.length;++i)a.addParameterValueById(this.motionManager.lipSyncIds[i],t,.8)}null==(r=this.physics)||r.evaluate(a,t),null==(o=this.pose)||o.updateParameters(a,t),this.emit("beforeModelUpdate"),a.update(),a.loadParameters()}updateFocus(){this.coreModel.addParameterValueById(this.idParamEyeBallX,this.focusController.x),this.coreModel.addParameterValueById(this.idParamEyeBallY,this.focusController.y),this.coreModel.addParameterValueById(this.idParamAngleX,30*this.focusController.x),this.coreModel.addParameterValueById(this.idParamAngleY,30*this.focusController.y),this.coreModel.addParameterValueById(this.idParamAngleZ,this.focusController.x*this.focusController.y*-30),this.coreModel.addParameterValueById(this.idParamBodyAngleX,10*this.focusController.x)}updateFacialEmotion(t){this.coreModel.addParameterValueById(this.idParamMouthForm,t)}updateNaturalMovements(t,e){var i;null==(i=this.breath)||i.updateParameters(this.coreModel,t/1e3)}draw(t){const e=this.drawingMatrix,i=oi.getArray();i[0]=e.a,i[1]=e.b,i[4]=-e.c,i[5]=-e.d,i[12]=e.tx,i[13]=e.ty,this.renderer.setMvpMatrix(oi),this.renderer.setRenderState(t.getParameter(t.FRAMEBUFFER_BINDING),this.viewport),this.renderer.drawModel()}destroy(){super.destroy(),this.renderer.release(),this.coreModel.release(),this.renderer=void 0,this.coreModel=void 0}}class ni extends ye{constructor(t){if(super(t),o(this,"moc"),o(this,"textures"),!ni.isValidJSON(t))throw new TypeError("Invalid JSON.");Object.assign(this,new ee(t))}static isValidJSON(t){var e;return!!(null==t?void 0:t.FileReferences)&&"string"==typeof t.FileReferences.Moc&&(null==(e=t.FileReferences.Textures)?void 0:e.length)>0&&t.FileReferences.Textures.every((t=>"string"==typeof t))}replaceFiles(t){if(super.replaceFiles(t),this.motions)for(const[e,i]of Object.entries(this.motions))for(let s=0;s<i.length;s++)i[s].File=t(i[s].File,`motions.${e}[${s}].File`),void 0!==i[s].Sound&&(i[s].Sound=t(i[s].Sound,`motions.${e}[${s}].Sound`));if(this.expressions)for(let e=0;e<this.expressions.length;e++)this.expressions[e].File=t(this.expressions[e].File,`expressions[${e}].File`)}}let li;ge(ni,[ee]);let hi=20;function ui(){return S.isStarted()?Promise.resolve():(null!=li||(li=new Promise(((t,e)=>{!function i(){try{di(),t()}catch(s){if(hi--,hi<0){const t=new Error("Failed to start up Cubism 4 framework.");return t.cause=s,void e(t)}de.log("Cubism4","Startup failed, retrying 10ms later..."),setTimeout(i,10)}}()}))),li)}function di(t){t=Object.assign({logFunction:console.log,loggingLevel:w.LogLevel_Verbose},t),S.startUp(t),S.initialize()}function ci(){var t;null==(t=this.__moc)||t.release()}Ye.registerRuntime({version:4,ready:ui,test:t=>t instanceof ni||ni.isValidJSON(t),isValidMoc(t){if(t.byteLength<4)return!1;const e=new Int8Array(t,0,4);return"MOC3"===String.fromCharCode(...e)},createModelSettings:t=>new ni(t),createCoreModel(t,e){const i=k.create(t,!!(null==e?void 0:e.checkMocConsistency));try{const t=i.createModel();return t.__moc=i,t}catch(s){try{i.release()}catch(r){}throw s}},createInternalModel(t,e,i){const s=new ai(t,e,i),r=t;return r.__moc&&(s.__moc=r.__moc,delete r.__moc,s.once("destroy",ci)),s},createPhysics:(t,e)=>yt.create(e),createPose:(t,e)=>c.create(e)}),t.ACubismMotion=N,t.BreathParameterData=l,t.CSM_ASSERT=T,t.Constant=b,t.Cubism4ExpressionManager=si,t.Cubism4InternalModel=ai,t.Cubism4ModelSettings=ni,t.Cubism4MotionManager=ri,t.CubismBlendMode=C,t.CubismBreath=n,t.CubismClippingContext=zt,t.CubismClippingManager_WebGL=Gt,t.CubismDebug=A,t.CubismExpressionMotion=G,t.CubismEyeBlink=u,t.CubismFramework=S,t.CubismLogDebug=I,t.CubismLogError=F,t.CubismLogInfo=E,t.CubismLogVerbose=function(t,...e){A.print(w.LogLevel_Verbose,"[CSM][V]"+t+"\n",e)},t.CubismLogWarning=L,t.CubismMath=_,t.CubismMatrix44=f,t.CubismMoc=k,t.CubismModel=O,t.CubismModelSettingsJson=ee,t.CubismModelUserData=V,t.CubismModelUserDataJson=U,t.CubismMotion=nt,t.CubismMotionCurve=q,t.CubismMotionCurveTarget=j,t.CubismMotionData=J,t.CubismMotionEvent=$,t.CubismMotionJson=Z,t.CubismMotionManager=class extends ht{constructor(){super(),this._currentPriority=0,this._reservePriority=0}getCurrentPriority(){return this._currentPriority}getReservePriority(){return this._reservePriority}setReservePriority(t){this._reservePriority=t}startMotionPriority(t,e,i){return i==this._reservePriority&&(this._reservePriority=0),this._currentPriority=i,super.startMotion(t,e,this._userTimeSeconds)}updateMotion(t,e){this._userTimeSeconds+=e;const i=super.doUpdateMotion(t,this._userTimeSeconds);return this.isFinished()&&(this._currentPriority=0),i}reserveMotion(t){return!(t<=this._reservePriority||t<=this._currentPriority)&&(this._reservePriority=t,!0)}},t.CubismMotionPoint=W,t.CubismMotionQueueEntry=lt,t.CubismMotionQueueManager=ht,t.CubismMotionSegment=Y,t.CubismMotionSegmentType=H,t.CubismPhysics=yt,t.CubismPhysicsInput=pt,t.CubismPhysicsJson=xt,t.CubismPhysicsOutput=_t,t.CubismPhysicsParticle=gt,t.CubismPhysicsRig=ft,t.CubismPhysicsSource=ct,t.CubismPhysicsSubRig=mt,t.CubismPhysicsTargetType=dt,t.CubismPose=c,t.CubismRenderTextureResource=Xt,t.CubismRenderer=x,t.CubismRendererProfile_WebGL=jt,t.CubismRenderer_WebGL=te,t.CubismShader_WebGL=Ht,t.CubismTextureColor=y,t.CubismVector2=m,t.DrawableColorData=D,t.DrawableCullingData=R,t.EvaluationOptionFlag=Q,t.ExpressionBlendType=z,t.ExpressionManager=_e,t.EyeState=d,t.FileLoader=He,t.FocusController=Ce,t.HitAreaBody="Body",t.HitAreaHead="Head",t.HitAreaPrefix="HitArea",t.InternalModel=Ie,t.InvalidMotionQueueEntryHandleValue=ut,t.LOGICAL_HEIGHT=2,t.LOGICAL_WIDTH=2,t.Live2DFactory=Ye,t.Live2DLoader=De,t.Live2DModel=ii,t.Live2DTransform=Ke,t.LogLevel=w,t.ModelSettings=ye,t.MotionManager=we,t.MotionPreloadStrategy=Se,t.MotionPriority=Me,t.MotionState=ve,t.Options=Mt,t.ParamAngleX=ie,t.ParamAngleY=se,t.ParamAngleZ=re,t.ParamArmLA="ParamArmLA",t.ParamArmLB="ParamArmLB",t.ParamArmRA="ParamArmRA",t.ParamArmRB="ParamArmRB",t.ParamBaseX="ParamBaseX",t.ParamBaseY="ParamBaseY",t.ParamBodyAngleX=le,t.ParamBodyAngleY="ParamBodyAngleY",t.ParamBodyAngleZ="ParamBodyAngleZ",t.ParamBreath=he,t.ParamBrowLAngle="ParamBrowLAngle",t.ParamBrowLForm="ParamBrowLForm",t.ParamBrowLX="ParamBrowLX",t.ParamBrowLY="ParamBrowLY",t.ParamBrowRAngle="ParamBrowRAngle",t.ParamBrowRForm="ParamBrowRForm",t.ParamBrowRX="ParamBrowRX",t.ParamBrowRY="ParamBrowRY",t.ParamBustX="ParamBustX",t.ParamBustY="ParamBustY",t.ParamCheek="ParamCheek",t.ParamEyeBallForm="ParamEyeBallForm",t.ParamEyeBallX=oe,t.ParamEyeBallY=ae,t.ParamEyeLOpen="ParamEyeLOpen",t.ParamEyeLSmile="ParamEyeLSmile",t.ParamEyeROpen="ParamEyeROpen",t.ParamEyeRSmile="ParamEyeRSmile",t.ParamHairBack="ParamHairBack",t.ParamHairFluffy="ParamHairFluffy",t.ParamHairFront="ParamHairFront",t.ParamHairSide="ParamHairSide",t.ParamHandL="ParamHandL",t.ParamHandR="ParamHandR",t.ParamMouthForm=ne,t.ParamMouthOpenY="ParamMouthOpenY",t.ParamNONE="NONE:",t.ParamShoulderY="ParamShoulderY",t.PartColorData=B,t.PartData=g,t.PartsArmLPrefix="Parts01ArmL_",t.PartsArmPrefix="Parts01Arm_",t.PartsArmRPrefix="Parts01ArmR_",t.PartsIdCore="Parts01Core",t.PhysicsJsonEffectiveForces=class{constructor(){this.gravity=new m(0,0),this.wind=new m(0,0)}},t.PhysicsOutput=vt,t.ShaderNames=Wt,t.SoundManager=be,t.VERSION="v0.5.0-beta",t.VOLUME=Pe,t.XHRLoader=Fe,t.ZipLoader=ze,t.applyMixins=ge,t.clamp=ce,t.config=ue,t.copyArray=function(t,e,i,s,r){const o=e[s];Array.isArray(o)&&(i[r]=o.filter((e=>null!==e&&typeof e===t)))},t.copyProperty=function(t,e,i,s,r){const o=e[s];null!==o&&typeof o===t&&(i[r]=o)},t.csmRect=kt,t.cubism4Ready=ui,t.folderName=me,t.fragmentShaderSrcMaskInvertedPremultipliedAlpha=Kt,t.fragmentShaderSrcMaskPremultipliedAlpha=Qt,t.fragmentShaderSrcPremultipliedAlpha=Zt,t.fragmentShaderSrcsetupMask=qt,t.logger=de,t.rand=function(t,e){return Math.random()*(e-t)+t},t.remove=pe,t.startUpCubism4=di,t.vertexShaderSrc=$t,t.vertexShaderSrcMasked=Jt,t.vertexShaderSrcSetupMask=Yt,Object.defineProperty(t,Symbol.toStringTag,{value:"Module"})}));
