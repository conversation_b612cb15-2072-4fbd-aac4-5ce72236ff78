{"Version": 3, "Meta": {"PhysicsSettingCount": 48, "TotalInputCount": 121, "TotalOutputCount": 170, "VertexCount": 270, "Fps": 120, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "缩眼"}, {"Id": "PhysicsSetting2", "Name": "高光"}, {"Id": "PhysicsSetting3", "Name": "物理眼"}, {"Id": "PhysicsSetting4", "Name": "歪嘴"}, {"Id": "PhysicsSetting5", "Name": "瞪眼"}, {"Id": "PhysicsSetting6", "Name": "1"}, {"Id": "PhysicsSetting7", "Name": "2"}, {"Id": "PhysicsSetting8", "Name": "3"}, {"Id": "PhysicsSetting9", "Name": "4"}, {"Id": "PhysicsSetting10", "Name": "5"}, {"Id": "PhysicsSetting11", "Name": "6"}, {"Id": "PhysicsSetting12", "Name": "7"}, {"Id": "PhysicsSetting13", "Name": "8"}, {"Id": "PhysicsSetting14", "Name": "9"}, {"Id": "PhysicsSetting15", "Name": "10"}, {"Id": "PhysicsSetting16", "Name": "11"}, {"Id": "PhysicsSetting17", "Name": "12"}, {"Id": "PhysicsSetting18", "Name": "13"}, {"Id": "PhysicsSetting19", "Name": "14"}, {"Id": "PhysicsSetting20", "Name": "胸x"}, {"Id": "PhysicsSetting21", "Name": "z1"}, {"Id": "PhysicsSetting22", "Name": "z"}, {"Id": "PhysicsSetting23", "Name": "y"}, {"Id": "PhysicsSetting24", "Name": "x"}, {"Id": "PhysicsSetting25", "Name": "裙子x"}, {"Id": "PhysicsSetting26", "Name": "裙子y"}, {"Id": "PhysicsSetting27", "Name": "15"}, {"Id": "PhysicsSetting28", "Name": "16"}, {"Id": "PhysicsSetting29", "Name": "17"}, {"Id": "PhysicsSetting30", "Name": "18"}, {"Id": "PhysicsSetting31", "Name": "腰带"}, {"Id": "PhysicsSetting32", "Name": "19"}, {"Id": "PhysicsSetting33", "Name": "衣领"}, {"Id": "PhysicsSetting34", "Name": "衣领(2)"}, {"Id": "PhysicsSetting35", "Name": "手y"}, {"Id": "PhysicsSetting36", "Name": "左手"}, {"Id": "PhysicsSetting37", "Name": "右手"}, {"Id": "PhysicsSetting38", "Name": "20"}, {"Id": "PhysicsSetting39", "Name": "21"}, {"Id": "PhysicsSetting40", "Name": "22"}, {"Id": "PhysicsSetting41", "Name": "23"}, {"Id": "PhysicsSetting42", "Name": "捂脸左手"}, {"Id": "PhysicsSetting43", "Name": "捂脸右手"}, {"Id": "PhysicsSetting44", "Name": "24"}, {"Id": "PhysicsSetting45", "Name": "25"}, {"Id": "PhysicsSetting46", "Name": "耶1"}, {"Id": "PhysicsSetting47", "Name": "水滴"}, {"Id": "PhysicsSetting48", "Name": "26"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param"}, "VertexIndex": 1, "Scale": 12, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param2"}, "VertexIndex": 2, "Scale": 12, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param3"}, "VertexIndex": 3, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 1, "Acceleration": 0.85, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 1, "Acceleration": 0.85, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 2, "Acceleration": 1.5, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param4"}, "VertexIndex": 1, "Scale": 12, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param5"}, "VertexIndex": 2, "Scale": 12, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.95, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.95, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 1, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 2, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.98, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.98, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "Param8"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param9"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.95, "Delay": 0.1, "Acceleration": 0.1, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBrowLY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param10"}, "VertexIndex": 1, "Scale": 65, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.4, "Acceleration": 0.1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation2"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation3"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation4"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation5"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation6"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation7"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation8"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation9"}, "VertexIndex": 4, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation10"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation11"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation12"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation13"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation14"}, "VertexIndex": 5, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 17}, {"Position": {"X": 0, "Y": 17}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 17}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 17}, {"Position": {"X": 0, "Y": 51}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 17}, {"Position": {"X": 0, "Y": 68}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 17}, {"Position": {"X": 0, "Y": 85}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 17}, {"Position": {"X": 0, "Y": 102}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 17}, {"Position": {"X": 0, "Y": 119}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 17}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation15"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation16"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation17"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation18"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation19"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation20"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation21"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation22"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation23"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation24"}, "VertexIndex": 5, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 18}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 18}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 18}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 18}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 18}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 18}, {"Position": {"X": 0, "Y": 108}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 18}, {"Position": {"X": 0, "Y": 126}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 18}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation25"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation26"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation27"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation28"}, "VertexIndex": 4, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation29"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation30"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation31"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation32"}, "VertexIndex": 4, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation33"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation34"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation35"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation36"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation37"}, "VertexIndex": 5, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 18}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 18}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 18}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 18}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 18}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 18}, {"Position": {"X": 0, "Y": 108}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 18}, {"Position": {"X": 0, "Y": 126}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 18}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation38"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation39"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation40"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation41"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation42"}, "VertexIndex": 5, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation43"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation44"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation45"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation46"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation47"}, "VertexIndex": 5, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 19}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 19}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 19}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 19}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 19}, {"Position": {"X": 0, "Y": 100}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 19}, {"Position": {"X": 0, "Y": 120}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 19}, {"Position": {"X": 0, "Y": 140}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 19}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation48"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation49"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation50"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation51"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation52"}, "VertexIndex": 5, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 19}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 19}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 19}, {"Position": {"X": 0, "Y": 57}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 19}, {"Position": {"X": 0, "Y": 76}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 19}, {"Position": {"X": 0, "Y": 95}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 19}, {"Position": {"X": 0, "Y": 114}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 19}, {"Position": {"X": 0, "Y": 133}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 19}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation53"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation54"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation55"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation56"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation57"}, "VertexIndex": 5, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation58"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation59"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation60"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation61"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation62"}, "VertexIndex": 5, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param11"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param12"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 0.92, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.92, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.92, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ3"}, "VertexIndex": 1, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12.5}, "Mobility": 0.95, "Delay": 0.4, "Acceleration": 0.1, "Radius": 12.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12.5}, "Mobility": 0.95, "Delay": 0.4, "Acceleration": 0.1, "Radius": 12.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "VertexIndex": 1, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12.5}, "Mobility": 0.95, "Delay": 0.4, "Acceleration": 0.1, "Radius": 12.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12.5}, "Mobility": 0.95, "Delay": 0.4, "Acceleration": 0.1, "Radius": 12.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param13"}, "VertexIndex": 1, "Scale": 23, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param14"}, "VertexIndex": 2, "Scale": 23, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param15"}, "VertexIndex": 3, "Scale": 23, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.9, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param16"}, "VertexIndex": 1, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param17"}, "VertexIndex": 2, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param18"}, "VertexIndex": 3, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.85, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.9, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation65"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation66"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation67"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation68"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 1, "Radius": 18}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 18}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.86, "Radius": 18}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.87, "Radius": 18}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.89, "Radius": 18}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 0.9, "Radius": 18}, {"Position": {"X": 0, "Y": 108}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.91, "Radius": 18}, {"Position": {"X": 0, "Y": 126}, "Mobility": 0.89, "Delay": 0.9, "Acceleration": 0.93, "Radius": 18}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation69"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation70"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation71"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation72"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 1, "Radius": 18}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 18}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.86, "Radius": 18}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.87, "Radius": 18}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.89, "Radius": 18}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 0.9, "Radius": 18}, {"Position": {"X": 0, "Y": 108}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.91, "Radius": 18}, {"Position": {"X": 0, "Y": 126}, "Mobility": 0.89, "Delay": 0.9, "Acceleration": 0.93, "Radius": 18}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation73"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation74"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation75"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation76"}, "VertexIndex": 4, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation77"}, "VertexIndex": 5, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation78"}, "VertexIndex": 6, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 18}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 18}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 18}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 18}, {"Position": {"X": 0, "Y": 72}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 18}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 18}, {"Position": {"X": 0, "Y": 108}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 18}, {"Position": {"X": 0, "Y": 126}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 18}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation79"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation80"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation81"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param19"}, "VertexIndex": 1, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param20"}, "VertexIndex": 2, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 0.9, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation82"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation83"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation84"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param21"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param22"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation106"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation107"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.93, "Acceleration": 0.95, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 0.95, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 0.95, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 0.95, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting34", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param23"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param24"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation108"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation109"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.93, "Acceleration": 0.95, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 0.95, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 0.95, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 0.95, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting35", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param25"}, "VertexIndex": 1, "Scale": 55, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.85, "Acceleration": 0.9, "Radius": 5}, {"Position": {"X": 0, "Y": 12.5}, "Mobility": 0.95, "Delay": 0.4, "Acceleration": 0.1, "Radius": 12.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting36", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation88"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation89"}, "VertexIndex": 2, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation90"}, "VertexIndex": 3, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation91"}, "VertexIndex": 4, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation92"}, "VertexIndex": 5, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.97, "Delay": 0.9, "Acceleration": 0.85, "Radius": 20}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 65}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 1.5, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting37", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation93"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation94"}, "VertexIndex": 2, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation95"}, "VertexIndex": 3, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation96"}, "VertexIndex": 4, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation97"}, "VertexIndex": 5, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.97, "Delay": 0.9, "Acceleration": 0.85, "Radius": 20}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 15}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 65}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 1.5, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting38", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation98"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation99"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation100"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.95, "Acceleration": 0.95, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.95, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.95, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.95, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting39", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation101"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation102"}, "VertexIndex": 2, "Scale": 7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation103"}, "VertexIndex": 3, "Scale": 7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.93, "Acceleration": 0.92, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 0.92, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 0.92, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 0.92, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting40", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation112"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation113"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation114"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation115"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation116"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.89, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting41", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation117"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation118"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation119"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation120"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation121"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.89, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting42", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation122"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation123"}, "VertexIndex": 2, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation124"}, "VertexIndex": 3, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 0.85, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting43", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation125"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation126"}, "VertexIndex": 2, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation127"}, "VertexIndex": 3, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 0.85, "Radius": 13}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 13}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 13}, {"Position": {"X": 0, "Y": 39}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 13}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting44", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation128"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation129"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation130"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting45", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation131"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation132"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation133"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation139"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation140"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation141"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation142"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation143"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation144"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting46", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation136"}, "VertexIndex": 1, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation137"}, "VertexIndex": 2, "Scale": 6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation138"}, "VertexIndex": 3, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.95, "Acceleration": 0.92, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.92, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.92, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.92, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting47", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param34"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param35"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param36"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 0.92, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 1, "Acceleration": 0.92, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 1, "Acceleration": 0.92, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.95, "Delay": 1, "Acceleration": 0.92, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting48", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation148"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation149"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation150"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation151"}, "VertexIndex": 1, "Scale": 0, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation152"}, "VertexIndex": 2, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation153"}, "VertexIndex": 3, "Scale": 9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.86, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.87, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.89, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.91, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 0.91, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.89, "Delay": 0.85, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}