{"Version": 3, "Meta": {"Duration": 2.1, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 35, "TotalSegmentCount": 133, "TotalPointCount": 364, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.378, 0, 0.422, 0, 0.467, 0, 1, 0.556, 0, 0.644, 0, 0.733, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.378, 0, 0.422, -15, 0.467, -15, 1, 0.556, -15, 0.644, 11, 0.733, 11, 0, 2.1, 11]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.444, 0, 0.556, -15, 0.667, -15, 1, 0.722, -15, 0.778, -9, 0.833, -9, 0, 2.1, -9]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.067, 1, 0.133, 1, 0.2, 1, 1, 0.244, 1, 0.289, 1, 0.333, 1, 1, 0.456, 1, 0.578, 0, 0.7, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.456, 0, 0.578, 1, 0.7, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.067, 1, 0.133, 1, 0.2, 1, 1, 0.244, 1, 0.289, 1, 0.333, 1, 1, 0.456, 1, 0.578, 0, 0.7, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.456, 0, 0.578, 1, 0.7, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.456, 0, 0.578, -0.14, 0.7, -0.14, 0, 2.1, -0.14]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.456, 0, 0.578, -0.14, 0.7, -0.14, 0, 2.1, -0.14]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 1, 0.522, 0, 0.611, -0.21, 0.7, -0.21, 0, 2.1, -0.21]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 1, 0.522, 0, 0.611, 0.2, 0.7, 0.2, 0, 2.1, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 1, 0.522, 0, 0.611, 0.25, 0.7, 0.25, 0, 2.1, 0.25]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 1, 0.522, 0, 0.611, 0.23, 0.7, 0.23, 0, 2.1, 0.23]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 1, 0.522, 0, 0.611, 0, 0.7, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 1, 0.522, 0, 0.611, 0, 0.7, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.067, 1, 0.133, 1, 0.2, 1, 1, 0.278, 1, 0.356, 1, 0.433, 1, 1, 0.522, 1, 0.611, 1, 0.7, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 1, 0.522, 0, 0.611, 1, 0.7, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 8, 0.433, 8, 1, 0.5, 8, 0.567, -7, 0.633, -7, 1, 0.689, -7, 0.744, 3, 0.8, 3, 1, 0.844, 3, 0.889, 0, 0.933, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 1, 0.511, 0, 0.589, 4, 0.667, 4, 1, 0.722, 4, 0.778, 2, 0.833, 2, 0, 2.1, 2]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamShoulder", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, -1, 0.433, -1, 1, 0.5, -1, 0.567, 1, 0.633, 1, 1, 0.689, 1, 0.744, -0.4, 0.8, -0.4, 1, 0.956, -0.4, 1.111, 0, 1.267, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamLeg", "Segments": [0, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, -5.208, 0.333, -5.208, 1, 0.4, -5.208, 0.467, 9.583, 0.533, 9.583, 0, 2.1, 9.583]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, -4.583, 0.333, -4.583, 1, 0.4, -4.583, 0.467, 4.375, 0.533, 4.375, 0, 2.1, 4.375]}, {"Target": "Parameter", "Id": "ParamHandLB", "Segments": [0, 10, 1, 0.122, 10, 0.244, 8.89, 0.367, 4.167, 1, 0.411, 2.449, 0.456, -4.583, 0.5, -4.583, 1, 0.544, -4.583, 0.589, 4.792, 0.633, 4.792, 0, 2.1, 4.792]}, {"Target": "Parameter", "Id": "ParamHandRB", "Segments": [0, 10, 1, 0.122, 10, 0.244, 8.89, 0.367, 4.167, 1, 0.411, 2.449, 0.456, -4.583, 0.5, -4.583, 1, 0.544, -4.583, 0.589, 7.292, 0.633, 7.292, 0, 2.1, 7.292]}, {"Target": "Parameter", "Id": "ParamHairAhoge", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, -0.142, 0.333, 0.234, 1, 0.411, 0.89, 0.489, 10, 0.567, 10, 1, 0.611, 10, 0.656, -10, 0.7, -10, 1, 0.767, -10, 0.833, 9.951, 0.9, 9.951, 1, 0.978, 9.951, 1.056, -9.508, 1.133, -9.508, 1, 1.211, -9.508, 1.289, 5.892, 1.367, 5.892, 1, 1.433, 5.892, 1.5, -2, 1.567, -2, 1, 1.611, -2, 1.656, 1, 1.7, 1, 1, 1.756, 1, 1.811, 0, 1.867, 0, 0, 2.1, 0]}, {"Target": "PartOpacity", "Id": "PartArmA", "Segments": [0, 0, 0, 2.1, 0]}, {"Target": "PartOpacity", "Id": "PartArmB", "Segments": [0, 1, 0, 2.1, 1]}]}