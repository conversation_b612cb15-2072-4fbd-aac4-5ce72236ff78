<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <script src="./js/live2dcubismcore.min.js"></script>
    <script src="./js/live2d.min.js"></script>
    <script src="./js/pixi.min.js"></script>

    <!-- if only Cubism 4 support-->
    <script src="./js/cubism4.min.js"></script>
    <script src="./js/jquery-3.1.1.min.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <title>live2dSpeek</title>
</head>

<body>
<canvas id=canvas></canvas>
<div id="control">
    <div class="label">1、测试说话</div>
    <button id="play">测试音频</button>
    <br/><br/>
    <div class="label">2、调用接口生成音频</div>
    <textarea id="text" style="width:400px;height:300px;">你好，欢迎光临</textarea>
    <br/><br/>
    <button id="start">开始说话</button>
</div>

<script type="text/javascript">
    // 数字人模型
    const cubism4Model = "./assets/kei_vowels_pro/kei_vowels_pro.model3.json";
    // const cubism4Model = "./assets/Hiyori/Hiyori.model3.json";
    // const cubism4Model = "./assets/March 7th/March 7th.model3.json";
    // const cubism4Model = "./assets/haru/haru_greeter_t03.model3.json";


    const live2d = PIXI.live2d;
    (async function main() {
        const app = new PIXI.Application({
            view: document.getElementById("canvas"),
            autoStart: true,
            resizeTo: window,
            backgroundColor: 0x333333
        });

        const models = await Promise.all([
            live2d.Live2DModel.from(cubism4Model)
        ]);

        models.forEach((model) => {
            app.stage.addChild(model);

            const scaleX = (innerWidth) / model.width;
            const scaleY = (innerHeight) / model.height;

            // fit the window
            model.scale.set(Math.min(scaleX, scaleY));
            model.y = innerHeight * 0.1;
            draggable(model);
        });

        const model4 = models[0];
        console.log(innerWidth)
        // model4.x = innerWidth / 2;
        // 居中显示
        model4.x = (innerWidth - model4.width) / 2;

        model4.on("hit", (hitAreas) => {
            if (hitAreas.includes("Body")) {
                model4.motion("Tap");
            }

            if (hitAreas.includes("Head")) {
                model4.expression();
            }
        });


        $("#play").click(function () {
            talk(model4, "./demo.mp3");
        });

        $("#start").click(function () {
            console.log($("#text").val());
            let text = $("#text").val().trim();
            if (text == "") {
                alert("请输入内容");
                return false;
            }
            $("#start").prop("disabled", true);
            axios.get("http://127.0.0.1:2020/dealAudio?file_name=test.mp3&voice=xiaoxiao&text=" + text)
                .then(response => {
                    console.log(response.data);
                    const audioUrl = response.data + "?v=" + new Date().getTime();
                    talk(model4, audioUrl);
                    $("#start").prop("disabled", false);
                })
                .catch(error => {
                    console.error('请求接口失败:', error);
                    $("#start").prop("disabled", false);
                });
        });


    })();


    function talk(model, audio) {
        var audio_link = audio;  //[Optional arg, can be null or empty] [relative or full url path] [mp3 or wav file] "./Keira.wav"
        var volume = 1; // [Optional arg, can be null or empty] [0.0 - 1.0]
        var expression = 8; // [Optional arg, can be null or empty] [index|name of expression]
        var resetExpression = true; // [Optional arg, can be null or empty] [true|false] [default: true] [if true, expression will be reset to default after animation is over]
        var crossOrigin = "anonymous"; // [Optional arg, to use not same-origin audios] [DEFAULT: null]

        model.speak(audio_link, {
            volume: volume,
            expression: expression,
            resetExpression: resetExpression,
            crossOrigin: crossOrigin
        })
        model.speak(audio_link)
        model.speak(audio_link, {volume: volume})
        model.speak(audio_link, {expression: expression, resetExpression: resetExpression})

    }


    function draggable(model) {
        model.buttonMode = true;
        model.on("pointerdown", (e) => {
            model.dragging = true;
            model._pointerX = e.data.global.x - model.x;
            model._pointerY = e.data.global.y - model.y;
        });
        model.on("pointermove", (e) => {
            if (model.dragging) {
                model.position.x = e.data.global.x - model._pointerX;
                model.position.y = e.data.global.y - model._pointerY;
            }
        });
        model.on("pointerupoutside", () => (model.dragging = false));
        model.on("pointerup", () => (model.dragging = false));
    }

</script>


<style>
    #control {
        position: absolute;
        top: 50px;
        left: 50px;
        color: #ffffff;
        font-size: 18px;
    }

    .label {
        font-size: 32px;
        font-weight: 800;
    }
</style>

</body>
</html>
